# Enhanced build script for frontend with bundling and minification
param(
    [string]$Version = $null,
    [switch]$Dev = $false,
    [switch]$Watch = $false,
    [switch]$Clean = $false
)

$ErrorActionPreference = "Stop"

# Colors for output
$ColorGreen = "Green"
$ColorYellow = "Yellow"
$ColorCyan = "Cyan"
$ColorRed = "Red"

Write-Host "🔨 Building frontend with bundling and minification..." -ForegroundColor $ColorGreen

# Get current timestamp or use provided version
if ([string]::IsNullOrEmpty($Version)) {
    $Version = Get-Date -Format "yyyyMMddHHmmss"
}

Write-Host "Using version: $Version" -ForegroundColor $ColorCyan

# Change to frontend directory
$FrontendDir = "frontend"
if (-not (Test-Path $FrontendDir)) {
    Write-Host "❌ Frontend directory not found" -ForegroundColor $ColorRed
    exit 1
}

Set-Location $FrontendDir

# Clean if requested
if ($Clean) {
    Write-Host "🧹 Cleaning previous build..." -ForegroundColor $ColorYellow
    if (Test-Path "dist") {
        Remove-Item -Recurse -Force "dist"
    }
    if (Test-Path "node_modules") {
        Remove-Item -Recurse -Force "node_modules"
    }
}

# Install dependencies if node_modules doesn't exist
if (-not (Test-Path "node_modules")) {
    Write-Host "📦 Installing dependencies..." -ForegroundColor $ColorYellow
    try {
        npm install
        if ($LASTEXITCODE -ne 0) {
            throw "npm install failed"
        }
    } catch {
        Write-Host "❌ Failed to install dependencies: $_" -ForegroundColor $ColorRed
        exit 1
    }
}

# Set environment variables
$env:CACHE_VERSION = $Version
$env:NODE_ENV = if ($Dev) { "development" } else { "production" }

# Build arguments
$BuildArgs = @()
if ($Dev) {
    $BuildArgs += "--dev"
}
if ($Watch) {
    $BuildArgs += "--watch"
}

# Run the build
Write-Host "⚙️  Running bundler..." -ForegroundColor $ColorYellow
try {
    if ($BuildArgs.Count -gt 0) {
        node build.js $BuildArgs
    } else {
        node build.js
    }
    
    if ($LASTEXITCODE -ne 0) {
        throw "Build process failed"
    }
} catch {
    Write-Host "❌ Build failed: $_" -ForegroundColor $ColorRed
    exit 1
}

# Go back to root directory
Set-Location ..

# Ensure backend/static directory exists
if (-not (Test-Path "backend/static")) {
    New-Item -ItemType Directory -Path "backend/static" -Force
}

# Copy bundled files to backend/static
Write-Host "📁 Copying bundled files to backend/static..." -ForegroundColor $ColorYellow
try {
    # Clean up old files first
    if (Test-Path "backend/static") {
        Remove-Item "backend/static/main-*.js", "backend/static/main-*.css" -ErrorAction SilentlyContinue
    }
    
    # Copy all files from frontend/dist to backend/static
    Copy-Item "frontend/dist/*" "backend/static/" -Recurse -Force
    
    # Copy themes directory from frontend to backend/static
    if (Test-Path "frontend/themes") {
        Write-Host "📁 Copying themes directory..." -ForegroundColor $ColorYellow
        Copy-Item "frontend/themes" "backend/static/" -Recurse -Force
        Write-Host "✅ Copied themes directory" -ForegroundColor $ColorGreen
    }
    
    # Copy HTML template files from frontend/src to backend/static
    if (Test-Path "frontend/src/deposits.html") {
        Copy-Item "frontend/src/deposits.html" "backend/static/deposits.html" -Force
        Write-Host "✅ Copied deposits.html template" -ForegroundColor $ColorGreen
    }
    
    # List the files that were copied
    Write-Host "✅ Copied files:" -ForegroundColor $ColorGreen
    Get-ChildItem "backend/static" | ForEach-Object {
        $Size = if ($_.PSIsContainer) { "DIR" } else { "{0:N2} KB" -f ($_.Length / 1024) }
        Write-Host "   $($_.Name) ($Size)" -ForegroundColor $ColorCyan
    }
    
} catch {
    Write-Host "❌ Failed to copy files: $_" -ForegroundColor $ColorRed
    exit 1
}

Write-Host "🎉 Frontend build completed successfully!" -ForegroundColor $ColorGreen
Write-Host "   Version: $Version" -ForegroundColor $ColorCyan
Write-Host "   Mode: $(if ($Dev) { 'Development' } else { 'Production' })" -ForegroundColor $ColorCyan
Write-Host "   Output: backend/static/" -ForegroundColor $ColorCyan 