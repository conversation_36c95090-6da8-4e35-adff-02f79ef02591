// # odata.rs
//
// OData query parameter parsing and processing utilities.
// This module provides comprehensive parsing of OData v4 query parameters including
// $filter, $orderby, and pagination options, with support for complex filter expressions.
//
// Key design choices:
// - Uses regex for robust pattern matching in filter expressions
// - Supports multiple terminal ID patterns (both 'in' syntax and 'eq' syntax)
// - Handles ISO 8601 datetime parsing with timezone conversion
// - Provides both simple and enhanced filter parsing for different endpoint needs
// - Includes comprehensive test coverage for various OData query scenarios
// - Implements consistent error handling and debug logging for troubleshooting

use chrono::{DateTime, Utc};
use regex::Regex;
use tracing::debug;

/// Parses OData $filter expressions to extract terminal IDs and date ranges.
pub fn parse_odata_filter(
    filter_text: &str,
) -> (Vec<String>, Option<DateTime<Utc>>, Option<DateTime<Utc>>) {
    debug!("Parsing OData filter: {}", filter_text);

    // Extract terminal IDs from filter like: TerminalId in ('ATMH0007','ATMH0008')
    let terminal_re = Regex::new(r"'([A-Z0-9]+)'").unwrap();
    let terminal_ids: Vec<String> = terminal_re
        .captures_iter(filter_text)
        .map(|cap| cap[1].to_string())
        .collect();

    // Remove duplicates while preserving order
    let mut unique_ids = Vec::new();
    for id in terminal_ids {
        if !unique_ids.contains(&id) {
            unique_ids.push(id);
        }
    }

    // Extract date ranges
    let ge_re = Regex::new(r"DepositDateTime\s+ge\s+([0-9T:\.Z\-]+)").unwrap();
    let le_re = Regex::new(r"DepositDateTime\s+le\s+([0-9T:\.Z\-]+)").unwrap();

    let start_dt = ge_re.captures(filter_text).and_then(|cap| {
        let date_str = cap[1].replace("Z", "+00:00");
        DateTime::parse_from_rfc3339(&date_str)
            .map(|dt| dt.with_timezone(&Utc))
            .ok()
    });

    let end_dt = le_re.captures(filter_text).and_then(|cap| {
        let date_str = cap[1].replace("Z", "+00:00");
        DateTime::parse_from_rfc3339(&date_str)
            .map(|dt| dt.with_timezone(&Utc))
            .ok()
    });

    debug!(
        "Parsed filter - IDs: {:?}, Start: {:?}, End: {:?}",
        unique_ids, start_dt, end_dt
    );

    (unique_ids, start_dt, end_dt)
}









 