apiVersion: apps/v1
kind: Deployment
metadata:
  name: myatm-embedded-ui
  namespace: crmuis
  labels:
    app: myatm-embedded-ui
spec:
  progressDeadlineSeconds: 600
  replicas: 0
  revisionHistoryLimit: 10
  selector:
    matchLabels:
      app: myatm-embedded-ui
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 25%
      maxUnavailable: 25%
  template:
    metadata:
      labels:
        app: myatm-embedded-ui
    spec:
      containers:
        - name: api
          image: dockerregistry.paycorp.co.za/crmuis/myatm-embedded-ui:2012
          imagePullPolicy: Always
          env:
            - name: SYNCFUSION_LICENSE_KEY 
              value: "shaun to provide"
            - name: DEPOSITS_EXTERNAL_API_URL
              value: http://merchant-deposits-myatm-api.crmuis.svc.cluster.local
            - name: DEPOSITS_EXTERNAL_API_TIMEOUT
              value: "60"    
            - name: ENVIRONMENT
              value: QA
            - name: APM_SERVICE_NAME
              value: myatm-embedded-ui
            - name: ELASTICSEARCH_INDEX_PREFIX
              value: myatm-embedded-ui
            - name: APM_SERVER_URL
              value: https://qaprdelk0.qaprod.co.za:8200
            - name: APM_SECRET_TOKEN
              value: 478K1%hq9yms
            - name: APM_ENV
              value: QA
            - name: APM_TRANSACTION_SAMPLE_RATE
              value: "1.0"
            - name: APM_TRANSACTION_MAX_SPANS
              value: "500"
            - name: APM_SERVICE_VERSION
              value: "1.0.0"
            - name: APM_LOG_LEVEL
              value: Error
            - name: LOG_LEVEL
              value: info
            - name: ELASTICSEARCH_URL
              value: https://qaprdelk0.qaprod.co.za:9200
            - name: ELASTICSEARCH_APIKEY
              value: YXBwbG9nZ2luZzo0RlE2ZXNyRGozTlpQNXR0

          ports:
            - name: tcp-8000
              containerPort: 8000
          livenessProbe:
            httpGet:
              path: /health/live
              port: 8000
              scheme: HTTP
            initialDelaySeconds: 2
            periodSeconds: 60
            failureThreshold: 3
            successThreshold: 1
            timeoutSeconds: 1
          readinessProbe:
            httpGet:
              path: /health/ready
              port: 8000
              scheme: HTTP
            initialDelaySeconds: 2
            periodSeconds: 30
            failureThreshold: 3
            successThreshold: 1
            timeoutSeconds: 1
          resources:
            limits:
              cpu: 100m
              memory: 100Mi
            requests:
              cpu: 2m
              memory: 2Mi    
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File

        - name: otel-collector
          image: otel/opentelemetry-collector-contrib:0.93.0
          imagePullPolicy: IfNotPresent
          args:
            - --config=/etc/otel-collector-config.yaml
          ports:
            - name: tcp-4318
              containerPort: 4318
            - name: tcp-4317
              containerPort: 4317
          volumeMounts:
            - name: otel-config
              mountPath: /etc/otel-collector-config.yaml
              subPath: otel-collector-config.yaml
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File

      dnsPolicy: ClusterFirst
      restartPolicy: Always
      schedulerName: default-scheduler
      terminationGracePeriodSeconds: 30
      volumes:
        - name: otel-config
          configMap:
            name: myatm-embedded-ui-inline-otel-config
            defaultMode: 420
