// # elasticsearch_layer.rs
//
// Custom tracing layer for direct log shipping to Elasticsearch clusters.
// This module provides a tracing subscriber layer that automatically ships structured
// logs to Elasticsearch for centralized log aggregation and analysis.
//
// Key design choices:
// - Implements tracing_subscriber::Layer trait for seamless integration with tracing infrastructure
// - Uses background async task with batching for efficient log shipping and performance
// - Supports both authenticated (base64 credentials) and unauthenticated connections
// - Automatically creates daily indices with proper log structure and metadata
// - Includes comprehensive error handling to prevent log shipping failures from affecting main application
// - Uses NDJSON bulk API for efficient batch log insertion into Elasticsearch

use chrono::{DateTime, Utc};
use elasticsearch::{
    http::transport::{SingleNodeConnectionPool, Transport},
    Elasticsearch,
};
use serde_json::{json, Value};
use std::collections::HashMap;
use std::sync::Arc;
use tokio::sync::mpsc;
use tracing::{Event, Subscriber};
use tracing_subscriber::{
    layer::{Context, Layer},
    registry::LookupSpan,
};
use url::Url;
use std::error::Error;

/// Tracing layer that ships logs directly to Elasticsearch for centralized log management.
/// 
/// This layer captures tracing events and ships them to Elasticsearch in structured format,
/// providing centralized log aggregation and search capabilities. It operates asynchronously
/// to minimize impact on application performance while ensuring comprehensive log coverage.
pub struct ElasticsearchLayer {
    #[allow(dead_code)]
    // Elasticsearch client retained for potential future direct query functionality
    client: Arc<Elasticsearch>,
    #[allow(dead_code)]
    // Index prefix stored for potential runtime index management features
    index_prefix: String,
    sender: mpsc::UnboundedSender<LogEntry>,
}

/// Internal log entry structure for batching and shipping to Elasticsearch.
/// 
/// Captures all relevant log metadata including timestamp, level, message, custom fields,
/// and source location information for comprehensive log analysis capabilities.
#[derive(Debug, Clone)]
struct LogEntry {
    timestamp: DateTime<Utc>,
    level: String,
    message: String,
    fields: HashMap<String, Value>,
    target: String,
    module_path: Option<String>,
    file: Option<String>,
    line: Option<u32>,
    #[allow(dead_code)]
    // Service name preserved for potential service-specific log routing or filtering
    service_name: String,
}

impl ElasticsearchLayer {
    /// Creates a new Elasticsearch layer with connection configuration.
    /// 
    /// Establishes connection to Elasticsearch cluster using provided URL and credentials,
    /// sets up background log shipping task, and configures index naming and service metadata.
    /// Supports both authenticated and unauthenticated connections based on environment configuration.
    pub fn new(
        elasticsearch_url: &str,
        index_prefix: &str,
        service_name: &str,
    ) -> Result<Self, Box<dyn std::error::Error + Send + Sync>> {
        let transport = if let Ok(encoded_credentials) = std::env::var("ELASTICSEARCH_APIKEY") {
            // Decode base64 encoded username:password
            use elasticsearch::auth::Credentials;
            use elasticsearch::http::transport::TransportBuilder;

            use base64::Engine;
            let engine = base64::engine::general_purpose::STANDARD;
            let decoded = match engine.decode(&encoded_credentials) {
                Ok(bytes) => String::from_utf8(bytes)
                    .map_err(|e| format!("Invalid UTF-8 in credentials: {}", e))?,
                Err(e) => return Err(format!("Failed to decode base64 credentials: {}", e).into()),
            };

            let parts: Vec<&str> = decoded.splitn(2, ':').collect();
            if parts.len() != 2 {
                return Err("Invalid credentials format. Expected username:password".into());
            }

            let username = parts[0].to_string();
            let password = parts[1].to_string();

            let url = Url::parse(elasticsearch_url)?;
            let conn_pool = SingleNodeConnectionPool::new(url);
            TransportBuilder::new(conn_pool)
                .auth(Credentials::Basic(username, password))
                .build()?
        } else {
            Transport::single_node(elasticsearch_url)?
        };

        let client = Arc::new(Elasticsearch::new(transport));

        // Log Elasticsearch connection details for debugging
        if std::env::var("ELASTICSEARCH_APIKEY").is_ok() {
            println!("Elasticsearch layer connecting to: {} (with authentication)", elasticsearch_url);
        } else {
            println!("Elasticsearch layer connecting to: {} (no authentication)", elasticsearch_url);
        }
        println!("Elasticsearch index prefix: {}", index_prefix);
        println!("Elasticsearch service name: {}", service_name);

        let (sender, receiver) = mpsc::unbounded_channel();

        // Spawn background task to handle log shipping
        let client_clone = Arc::clone(&client);
        let index_prefix_clone = index_prefix.to_string();
        let service_name = service_name.to_string();

        tokio::spawn(async move {
            Self::log_shipper(client_clone, receiver, index_prefix_clone, service_name).await;
        });

        Ok(Self {
            client,
            index_prefix: index_prefix.to_string(),
            sender,
        })
    }

    /// Background task that handles batched log shipping to Elasticsearch.
    /// 
    /// Receives log entries from the main application thread and ships them to Elasticsearch
    /// in batches for efficiency. Uses time-based and size-based batching to balance
    /// between performance and real-time log availability.
    async fn log_shipper(
        client: Arc<Elasticsearch>,
        mut receiver: mpsc::UnboundedReceiver<LogEntry>,
        index_prefix: String,
        service_name: String,
    ) {
        let mut batch = Vec::new();
        let mut interval = tokio::time::interval(std::time::Duration::from_secs(5));

        loop {
            tokio::select! {
                // Receive log entries
                entry = receiver.recv() => {
                    match entry {
                        Some(entry) => {
                            batch.push(entry);

                            // Send batch when it reaches a certain size
                            if batch.len() >= 100 {
                                Self::send_batch(&client, &index_prefix, &service_name, &mut batch).await;
                            }
                        }
                        None => break, // Channel closed
                    }
                }

                // Send batch periodically
                _ = interval.tick() => {
                    if !batch.is_empty() {
                        Self::send_batch(&client, &index_prefix, &service_name, &mut batch).await;
                    }
                }
            }
        }

        // Send any remaining logs
        if !batch.is_empty() {
            Self::send_batch(&client, &index_prefix, &service_name, &mut batch).await;
        }
    }

    /// Sends a batch of log entries to Elasticsearch using the bulk API.
    /// 
    /// Converts log entries to properly structured Elasticsearch documents and ships
    /// them using the bulk API for efficiency. Creates daily indices automatically
    /// and includes proper service metadata and log structure.
    async fn send_batch(
        client: &Elasticsearch,
        index_prefix: &str,
        service_name: &str,
        batch: &mut Vec<LogEntry>,
    ) {
        if batch.is_empty() {
            return;
        }

        let today = Utc::now().format("%Y.%m.%d");
        let index_name = format!("{}-{}", index_prefix, today);

        let mut body = Vec::new();

        for entry in batch.drain(..) {
            // Index action
            body.push(json!({
                "index": {
                    "_index": index_name
                }
            }));

            // Document
            let mut doc = json!({
                "@timestamp": entry.timestamp.to_rfc3339(),
                "level": entry.level,
                "message": entry.message,
                "logger": entry.target,
                "service": {
                    "name": service_name
                },
                "log": {
                    "level": entry.level.to_lowercase()
                }
            });

            // Add optional fields
            if let Some(module_path) = entry.module_path {
                doc["module_path"] = json!(module_path);
            }

            if let Some(file) = entry.file {
                doc["file"] = json!(file);
            }

            if let Some(line) = entry.line {
                doc["line"] = json!(line);
            }

            // Add custom fields
            for (key, value) in entry.fields {
                doc[key] = value;
            }

            body.push(doc);
        }

        // Send bulk request
        // Convert the JSON objects to NDJSON strings
        let ndjson_body: Vec<String> = body
            .into_iter()
            .map(|doc| serde_json::to_string(&doc).unwrap_or_default())
            .collect();

        match client
            .bulk(elasticsearch::BulkParts::None)
            .body(ndjson_body)
            .send()
            .await
        {
            Ok(response) => {
                // Check if the response indicates success
                if response.status_code().is_success() {
                    println!("Successfully sent {} log entries to Elasticsearch", batch.len());
                } else {
                    eprintln!("Elasticsearch bulk request failed with status: {}", response.status_code());
                    if let Ok(body) = response.text().await {
                        eprintln!("Response body: {}", body);
                    }
                }
            }
            Err(e) => {
                eprintln!("Failed to send logs to Elasticsearch: {}", e);
                eprintln!("Error details: {:?}", e);
                
                // Check if it's a network connectivity issue
                if let Some(source) = e.source() {
                    eprintln!("Error source: {}", source);
                }
                
                // Provide specific troubleshooting hints
                eprintln!("Troubleshooting suggestions:");
                eprintln!("1. Check network connectivity to: {}", "https://qaprdelk0.qaprod.co.za:9200");
                eprintln!("2. Verify SSL/TLS certificate is valid");
                eprintln!("3. Confirm Elasticsearch credentials are correct");
                eprintln!("4. Check if Elasticsearch service is running");
                eprintln!("5. Verify firewall/security group allows access to port 9200");
            }
        }
    }
}

impl<S> Layer<S> for ElasticsearchLayer
where
    S: Subscriber + for<'lookup> LookupSpan<'lookup>,
{
    fn on_event(&self, event: &Event<'_>, ctx: Context<'_, S>) {
        let metadata = event.metadata();
        let mut fields = HashMap::new();
        let mut message = String::new();

        // Extract event data
        let mut visitor = JsonVisitor::new(&mut fields, &mut message);
        event.record(&mut visitor);

        // If no explicit message was found, try to construct one from fields
        if message.is_empty() {
            if let Some(msg) = fields.get("message") {
                message = msg.to_string().trim_matches('"').to_string();
            }
        }

        // Get span context if available
        if let Some(span) = ctx.lookup_current() {
            // Add span fields to the log entry
            let extensions = span.extensions();
            if let Some(visitor) = extensions.get::<SpanFields>() {
                for (key, value) in &visitor.fields {
                    fields.insert(format!("span.{}", key), value.clone());
                }
            }

            // Add span name
            fields.insert("span.name".to_string(), json!(span.name()));
        }

        let entry = LogEntry {
            timestamp: Utc::now(),
            level: metadata.level().to_string(),
            message,
            fields,
            target: metadata.target().to_string(),
            module_path: metadata.module_path().map(|s| s.to_string()),
            file: metadata.file().map(|s| s.to_string()),
            line: metadata.line(),
            service_name: "merchant-deposits-api".to_string(),
        };

        // Send to background task (non-blocking)
        if let Err(_) = self.sender.send(entry) {
            eprintln!("Failed to send log entry to Elasticsearch shipper");
        }
    }

    fn on_new_span(
        &self,
        attrs: &tracing::span::Attributes<'_>,
        id: &tracing::span::Id,
        ctx: Context<'_, S>,
    ) {
        let span = ctx.span(id).expect("Span not found, this is a bug");
        let mut fields = HashMap::new();
        let mut message = String::new();

        let mut visitor = JsonVisitor::new(&mut fields, &mut message);
        attrs.record(&mut visitor);

        let span_fields = SpanFields { fields };
        span.extensions_mut().insert(span_fields);
    }
}

/// Helper struct to store span fields
#[derive(Debug)]
struct SpanFields {
    fields: HashMap<String, Value>,
}

/// Visitor to extract field values as JSON
struct JsonVisitor<'a> {
    fields: &'a mut HashMap<String, Value>,
    message: &'a mut String,
}

impl<'a> JsonVisitor<'a> {
    fn new(fields: &'a mut HashMap<String, Value>, message: &'a mut String) -> Self {
        Self { fields, message }
    }
}

impl tracing::field::Visit for JsonVisitor<'_> {
    fn record_f64(&mut self, field: &tracing::field::Field, value: f64) {
        self.fields.insert(field.name().to_string(), json!(value));
    }

    fn record_i64(&mut self, field: &tracing::field::Field, value: i64) {
        self.fields.insert(field.name().to_string(), json!(value));
    }

    fn record_u64(&mut self, field: &tracing::field::Field, value: u64) {
        self.fields.insert(field.name().to_string(), json!(value));
    }

    fn record_bool(&mut self, field: &tracing::field::Field, value: bool) {
        self.fields.insert(field.name().to_string(), json!(value));
    }

    fn record_str(&mut self, field: &tracing::field::Field, value: &str) {
        if field.name() == "message" {
            self.message.push_str(value);
        } else {
            self.fields.insert(field.name().to_string(), json!(value));
        }
    }

    fn record_debug(&mut self, field: &tracing::field::Field, value: &dyn std::fmt::Debug) {
        self.fields
            .insert(field.name().to_string(), json!(format!("{:?}", value)));
    }
} 