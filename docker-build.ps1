# Docker build script with cache busting support
param(
    [string]$Tag = "myatm-embedded-ui:latest",
    [string]$Version = $null,
    [string]$Registry = $null,
    [switch]$Push = $false,
    [switch]$NoCache = $false
)

$ErrorActionPreference = "Stop"

Write-Host "🐳 Building Docker image with cache busting..." -ForegroundColor Green

# Generate version if not provided
if ([string]::IsNullOrEmpty($Version)) {
    $Version = Get-Date -Format "yyyyMMddHHmmss"
}

Write-Host "📦 Using cache version: $Version" -ForegroundColor Cyan
Write-Host "🏷️  Building image: $Tag" -ForegroundColor Cyan

# Build Docker image with cache version
$buildArgs = @(
    "--build-arg", "CACHE_VERSION=$Version"
)

if ($NoCache) {
    $buildArgs += "--no-cache"
}

$buildArgs += @("-t", $Tag, ".")

Write-Host "🔨 Running: docker build $($buildArgs -join ' ')" -ForegroundColor Yellow

try {
    & docker build @buildArgs
    
    if ($LASTEXITCODE -ne 0) {
        throw "Docker build failed with exit code $LASTEXITCODE"
    }
    
    Write-Host "✅ Docker build completed successfully!" -ForegroundColor Green
    Write-Host "📋 Image: $Tag" -ForegroundColor White
    Write-Host "🏷️  Cache Version: $Version" -ForegroundColor White
    
    # Tag with registry if provided
    if ($Registry) {
        $registryTag = "$Registry/$Tag"
        Write-Host "🏷️  Tagging for registry: $registryTag" -ForegroundColor Cyan
        & docker tag $Tag $registryTag
        
        if ($Push) {
            Write-Host "📤 Pushing to registry..." -ForegroundColor Yellow
            & docker push $registryTag
            
            if ($LASTEXITCODE -eq 0) {
                Write-Host "✅ Push completed successfully!" -ForegroundColor Green
            } else {
                Write-Host "❌ Push failed!" -ForegroundColor Red
                exit $LASTEXITCODE
            }
        }
    }
    
    Write-Host "🚀 Build process completed!" -ForegroundColor Green
    
} catch {
    Write-Host "❌ Build failed: $_" -ForegroundColor Red
    exit 1
} 