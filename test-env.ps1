# Test script to verify environment variable loading
Write-Host "Testing environment variable loading..." -ForegroundColor Cyan

# Set test environment variables
$env:DEPOSITS_EXTERNAL_API_URL = "https://test-api.example.com"
$env:PORT = "8000"
$env:ENVIRONMENT = "dev"

Write-Host "Environment variables set:" -ForegroundColor Green
Write-Host "DEPOSITS_EXTERNAL_API_URL: $env:DEPOSITS_EXTERNAL_API_URL" -ForegroundColor White
Write-Host "PORT: $env:PORT" -ForegroundColor White
Write-Host "ENVIRONMENT: $env:ENVIRONMENT" -ForegroundColor White

# Check if backend can read these variables
Write-Host "`nRunning backend with test variables..." -ForegroundColor Yellow
Set-Location "backend"
cargo run --quiet | Select-String -Pattern "Warning.*env|Deposits API base URL|Running in environment" | Select-Object -First 3 