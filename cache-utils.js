/**
 * Cache busting utilities for JavaScript files
 */

/**
 * Load a script with cache busting
 * @param {string} src - The script source URL
 * @param {string} version - Optional version string, defaults to timestamp
 * @returns {Promise} Promise that resolves when script is loaded
 */
function loadScript(src, version = null) {
    return new Promise((resolve, reject) => {
        // Remove existing script if it exists
        const existingScript = document.querySelector(`script[src*="${src}"]`);
        if (existingScript) {
            existingScript.remove();
        }

        // Create new script element
        const script = document.createElement('script');
        const cacheBuster = version || Date.now();
        script.src = `${src}?v=${cacheBuster}`;
        script.onload = resolve;
        script.onerror = reject;
        
        document.head.appendChild(script);
    });
}

/**
 * Load multiple scripts with cache busting
 * @param {Array} scripts - Array of script URLs
 * @param {string} version - Optional version string
 * @returns {Promise} Promise that resolves when all scripts are loaded
 */
function loadScripts(scripts, version = null) {
    const promises = scripts.map(src => loadScript(src, version));
    return Promise.all(promises);
}

/**
 * Reload current page scripts with new cache version
 * @param {string} version - Optional version string
 */
function reloadScripts(version = null) {
    const scripts = Array.from(document.querySelectorAll('script[src]'));
    const cacheBuster = version || Date.now();
    
    scripts.forEach(script => {
        if (script.src && !script.src.includes('cdn.')) { // Don't reload CDN scripts
            const url = new URL(script.src);
            url.searchParams.set('v', cacheBuster);
            script.src = url.toString();
        }
    });
}

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { loadScript, loadScripts, reloadScripts };
} 