{"version": 3, "sources": ["../src/styles.css"], "sourcesContent": ["/* # styles.css */\r\n/*\r\n * Global Application Styles - MyATM Embedded UI\r\n *\r\n * This stylesheet provides the base styling for the MyATM Embedded UI application.\r\n * It includes global styles, utility classes, and foundational design elements\r\n * that are used across the entire application.\r\n *\r\n * ## Style Organization\r\n * - **Base Styles**: Typography, layout fundamentals\r\n * - **Cache Management**: Notification styles for cache updates\r\n * - **Development Tools**: Development-only UI controls\r\n * - **Responsive Design**: Mobile-first responsive utilities\r\n *\r\n * ## Design System\r\n * The styles follow a consistent design system with:\r\n * - Primary color: #007bff (blue)\r\n * - Success color: #4CAF50 (green)\r\n * - Info color: #2196F3 (light blue)\r\n * - Error color: #f44336 (red)\r\n * - Font family: system sans-serif stack\r\n *\r\n * ## Feature-Specific Styles\r\n * Feature-specific styles are maintained in separate files:\r\n * - deposits.css: Deposits page styling\r\n * - Individual component styles as needed\r\n *\r\n * ## Production Considerations\r\n * - Development controls are automatically hidden in production\r\n * - Cache notifications are optimized for performance\r\n * - Responsive design ensures mobile compatibility\r\n */\r\n\r\n/* Base application styles */\r\nbody {\r\n    font-family: sans-serif;\r\n    margin-top: 2px;\r\n    margin-left: 5px;\r\n    margin-right: 5px;\r\n    margin-bottom: 0px;\r\n}\r\n\r\n#Grid {\r\n    max-width: 900px;\r\n    margin: auto;\r\n}\r\n\r\n.cache-notification {\r\n    position: fixed;\r\n    top: 20px;\r\n    right: 20px;\r\n    background: #4CAF50;\r\n    color: white;\r\n    padding: 12px 20px;\r\n    border-radius: 6px;\r\n    font-size: 14px;\r\n    box-shadow: 0 4px 12px rgba(0,0,0,0.15);\r\n    z-index: 10000;\r\n    opacity: 0;\r\n    transform: translateX(100%);\r\n    transition: all 0.3s ease-in-out;\r\n    max-width: 300px;\r\n    display: none; /* Hidden by default - only shown when explicitly triggered */\r\n}\r\n\r\n.cache-notification.show {\r\n    opacity: 1;\r\n    transform: translateX(0);\r\n    display: block;\r\n}\r\n\r\n.cache-notification.updating {\r\n    background: #2196F3;\r\n}\r\n\r\n.cache-notification.error {\r\n    background: #f44336;\r\n}\r\n\r\n.dev-controls {\r\n    position: fixed;\r\n    bottom: 10px;\r\n    right: 10px;\r\n    background: #007bff;\r\n    color: white;\r\n    padding: 10px;\r\n    border-radius: 5px;\r\n    font-size: 12px;\r\n    z-index: 1000;\r\n}\r\n\r\n.dev-controls button {\r\n    background: white;\r\n    color: #007bff;\r\n    border: none;\r\n    padding: 5px 10px;\r\n    margin: 2px;\r\n    border-radius: 3px;\r\n    cursor: pointer;\r\n    font-size: 11px;\r\n}\r\n\r\n.dev-controls button:hover {\r\n    background: #f0f0f0;\r\n}\r\n\r\n/* Hide dev controls in production */\r\n@media (min-width: 1px) {\r\n    .dev-controls[data-production=\"true\"] {\r\n        display: none;\r\n    }\r\n} "], "mappings": ";AAkCA;AACI,eAAa;AACb,cAAY;AACZ,eAAa;AACb,gBAAc;AACd,iBAAe;AACnB;AAEA,CAAC;AACG,aAAW;AACX,UAAQ;AACZ;AAEA,CAAC;AACG,YAAU;AACV,OAAK;AACL,SAAO;AACP,cAAY;AACZ,SAAO;AACP,WAAS,KAAK;AACd,iBAAe;AACf,aAAW;AACX,cAAY,EAAE,IAAI,KAAK,KAAK,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC;AAClC,WAAS;AACT,WAAS;AACT,aAAW,WAAW;AACtB,cAAY,IAAI,KAAK;AACrB,aAAW;AACX,WAAS;AACb;AAEA,CAlBC,kBAkBkB,CAAC;AAChB,WAAS;AACT,aAAW,WAAW;AACtB,WAAS;AACb;AAEA,CAxBC,kBAwBkB,CAAC;AAChB,cAAY;AAChB;AAEA,CA5BC,kBA4BkB,CAAC;AAChB,cAAY;AAChB;AAEA,CAAC;AACG,YAAU;AACV,UAAQ;AACR,SAAO;AACP,cAAY;AACZ,SAAO;AACP,WAAS;AACT,iBAAe;AACf,aAAW;AACX,WAAS;AACb;AAEA,CAZC,aAYa;AACV,cAAY;AACZ,SAAO;AACP,UAAQ;AACR,WAAS,IAAI;AACb,UAAQ;AACR,iBAAe;AACf,UAAQ;AACR,aAAW;AACf;AAEA,CAvBC,aAuBa,MAAM;AAChB,cAAY;AAChB;AAGA,OAAO,CAAC,SAAS,EAAE;AACf,GA7BH,YA6BgB,CAAC;AACV,aAAS;AACb;AACJ;", "names": []}