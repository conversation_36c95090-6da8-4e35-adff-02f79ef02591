# Test Coverage Setup

This document describes the testing and code coverage setup for the myatm-embedded-ui backend.

## Structure

The unit tests have been moved to a dedicated `backend/tests/` directory:

- `backend/tests/config_tests.rs` - Tests for configuration management
- `backend/tests/deposits_config_tests.rs` - Tests for deposits configuration
- `backend/tests/deposits_handlers_tests.rs` - Tests for deposit handlers
- `backend/tests/deposits_odata_tests.rs` - Tests for OData parsing functionality

## Running Tests

### Basic Test Run

```powershell
# Run all tests
cargo test

# Run tests in a specific file
cargo test --test config_tests
```

### Code Coverage

Use the `run-tests.ps1` PowerShell script in the project root to run tests with code coverage:

```powershell
# Basic coverage run (generates HTML, LCOV, and stdout reports)
.\run-tests.ps1

# Run with specific output formats
.\run-tests.ps1 -Format "html,lcov"

# Run with verbose output
.\run-tests.ps1 -Verbose

# Open HTML report automatically after completion
.\run-tests.ps1 -Open

# Clean previous coverage data before running
.\run-tests.ps1 -Clean

# Set custom output directory
.\run-tests.ps1 -OutputDir "my-coverage"

# Fail if coverage is below threshold (e.g., 80%)
.\run-tests.ps1 -FailUnder "80"
```

## Coverage Reports

The script generates the following coverage reports:

### HTML Report
- **Location**: `backend/coverage/tarpaulin-report.html`
- **Usage**: Open in browser for interactive coverage visualization
- **Features**: Line-by-line coverage, file navigation, coverage statistics

### LCOV Report
- **Location**: `backend/coverage/lcov.info`
- **Usage**: Integration with IDEs and CI/CD systems
- **Compatible with**: VS Code Coverage Gutters, Codecov, Coveralls

### Stdout Report
- **Location**: Console output
- **Usage**: Quick coverage summary with uncovered lines listed

## Current Coverage Status

As of the latest run:
- **Overall Coverage**: 7.46% (47/630 lines)
- **Tested Modules**:
  - `config.rs`: 14/15 lines (93.3%)
  - `deposits/config.rs`: 9/9 lines (100%)
  - `deposits/odata.rs`: 24/25 lines (96%)

## Tools Used

- **cargo-tarpaulin**: Rust code coverage tool
- **LLVM Coverage**: Backend coverage engine (Windows)
- **Ptrace**: Backend coverage engine (Linux)

## CI/CD Integration

The LCOV file can be uploaded to coverage services:

### Codecov
```bash
# Upload to codecov.io
bash <(curl -s https://codecov.io/bash) -f backend/coverage/lcov.info
```

### Coveralls
```bash
# Upload to coveralls.io
cargo tarpaulin --coveralls $REPO_TOKEN
```

## IDE Integration

### VS Code
1. Install the "Coverage Gutters" extension
2. Configure it to look for `backend/coverage/lcov.info`
3. Use Ctrl+Shift+P → "Coverage Gutters: Toggle Coverage"

## Best Practices

1. **Run tests frequently** during development
2. **Aim for >80% coverage** on new code
3. **Review uncovered lines** to identify missing test cases
4. **Use the HTML report** for detailed coverage analysis
5. **Integration tests** complement unit tests for higher coverage

## Troubleshooting

### Windows-specific Issues
- The script uses LLVM coverage engine by default on Windows
- If you encounter issues, try running with `-Verbose` flag

### Missing Dependencies
- The script automatically installs `cargo-tarpaulin` if not present
- Ensure you have the latest Rust toolchain installed

### Performance
- Coverage runs take longer than regular tests
- Use `-Clean` flag if you encounter caching issues
- Consider running coverage on CI rather than locally for large projects

## Contributing

When adding new functionality:
1. Write unit tests in the appropriate `backend/tests/` file
2. Run coverage to ensure adequate test coverage
3. Add integration tests for complex workflows
4. Update this README if you add new test categories 