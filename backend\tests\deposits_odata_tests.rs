// deposits_odata_tests.rs

//! Deposits OData Query Parsing Unit Tests
//! 
//! This module contains unit tests for the OData query parsing functionality in the deposits
//! module of the ATM embedded UI backend. It validates the parsing of complex OData filter
//! expressions used for querying merchant deposit data from external APIs, including terminal
//! ID extraction and date range parsing.
//! 
//! ## Test Coverage:
//! 
//! ### 1. OData Filter Parsing
//! - Tests parsing of complex OData `$filter` expressions
//! - Validates extraction of terminal IDs from `TerminalId in ('...')` clauses
//! - Tests date range parsing from `DepositDateTime ge/le` expressions
//! - Ensures proper handling of UTC timestamp formats (ISO 8601)
//! 
//! ### 2. Terminal ID Extraction
//! - Tests single terminal ID parsing (`'ATMH0007'`)
//! - Tests multiple terminal ID parsing (`'ATMH0007','ATMH0008','ATMH0009'`)
//! - Validates proper string parsing and whitespace handling
//! - Ensures accurate terminal identifier extraction for ATM-specific queries
//! 
//! ### 3. Date Range Processing
//! - Tests parsing of `DepositDateTime ge` (greater than or equal) conditions
//! - Tests parsing of `DepositDateTime le` (less than or equal) conditions
//! - Validates proper UTC timezone handling with chrono library
//! - Ensures correct date range boundaries for deposit time filtering
//! 
//! ## OData Protocol Context:
//! 
//! ### OData Query Structure:
//! - **$filter Parameter**: Complex filter expressions for data querying
//! - **Terminal Filtering**: `TerminalId in ('...')` for ATM-specific data
//! - **Date Range Filtering**: `DepositDateTime ge/le` for time-based queries
//! - **Logical Operators**: `and` operators combining multiple filter conditions
//! 
//! ### Real-World Usage:
//! - **ATM Deposit Queries**: Filter deposits by specific terminal IDs
//! - **Time-based Filtering**: Query deposits within specific date ranges
//! - **Multi-terminal Support**: Handle queries across multiple ATM terminals
//! - **External API Integration**: Parse filters for merchant deposit API calls
//! 
//! ## Test Philosophy:
//! 
//! - **Parser Reliability**: Ensure robust parsing of complex OData expressions
//! - **Date Accuracy**: Validate precise date/time parsing for financial records
//! - **Terminal Specificity**: Test accurate terminal ID extraction for multi-ATM environments
//! - **Edge Case Handling**: Validate parsing of various OData filter formats
//! 
//! ## Important Technical Details:
//! 
//! ### OData Filter Format:
//! ```
//! TerminalId in ('ATMH0007','ATMH0008') and 
//! DepositDateTime ge 2025-06-01T00:00:00Z and 
//! DepositDateTime le 2025-06-30T23:59:59Z
//! ```
//! 
//! ### Terminal ID Patterns:
//! - **Single Terminal**: `TerminalId in ('ATMH0007')`
//! - **Multiple Terminals**: `TerminalId in ('ATMH0007','ATMH0008','ATMH0009')`
//! - **ATM Naming**: Terminal IDs follow pattern like 'ATMH0007', 'CXBH0006'
//! 
//! ### Date/Time Handling:
//! - **ISO 8601 Format**: `2025-06-01T00:00:00Z` with UTC timezone
//! - **Chrono Integration**: Uses chrono library for robust date/time parsing
//! - **Range Boundaries**: `ge` (>=) for start dates, `le` (<=) for end dates
//! - **UTC Timezone**: All timestamps processed in UTC for consistency
//! 
//! ## Testing Scenarios:
//! 
//! ### Single Terminal with Date Range:
//! - Tests parsing terminal ID and complete date range (start and end)
//! - Validates June 2025 monthly query example
//! - Ensures proper boundary date parsing (00:00:00 to 23:59:59)
//! 
//! ### Multiple Terminals with Start Date:
//! - Tests parsing multiple terminal IDs from single filter
//! - Validates partial date range parsing (start date only)
//! - Ensures proper array extraction of terminal identifiers
//! 
//! ## Dependencies:
//! 
//! These tests validate functionality in `src/deposits/odata.rs` including:
//! - `parse_odata_filter()` function implementation
//! - OData filter expression parsing logic
//! - Terminal ID extraction algorithms
//! - Date/time parsing with chrono library integration
//! 
//! ## Production Impact:
//! 
//! These tests ensure reliable:
//! - External merchant API query construction
//! - Accurate terminal-specific deposit filtering
//! - Correct date range processing for financial queries
//! - Robust OData protocol compliance
//! 
//! ## Running Tests:
//! 
//! ```bash
//! cargo test deposits_odata_tests
//! ```
//! 
//! For verbose output:
//! ```bash
//! cargo test deposits_odata_tests -- --nocapture
//! ```

use myatm_embedded_ui::deposits::odata::*;
use chrono::{TimeZone, Utc};

#[test]
fn test_parse_filter_single_terminal() {
    let filter = "TerminalId in ('ATMH0007') and DepositDateTime ge 2025-06-01T00:00:00Z and DepositDateTime le 2025-06-30T23:59:59Z";
    let (ids, start, end) = parse_odata_filter(filter);

    assert_eq!(ids, vec!["ATMH0007"]);
    assert_eq!(
        start.unwrap(),
        Utc.with_ymd_and_hms(2025, 6, 1, 0, 0, 0).unwrap()
    );
    assert_eq!(
        end.unwrap(),
        Utc.with_ymd_and_hms(2025, 6, 30, 23, 59, 59).unwrap()
    );
}

#[test]
fn test_parse_filter_multiple_terminals() {
    let filter = "TerminalId in ('ATMH0007','ATMH0008','ATMH0009') and DepositDateTime ge 2025-06-01T00:00:00Z";
    let (ids, start, _) = parse_odata_filter(filter);

    assert_eq!(ids, vec!["ATMH0007", "ATMH0008", "ATMH0009"]);
    assert_eq!(
        start.unwrap(),
        Utc.with_ymd_and_hms(2025, 6, 1, 0, 0, 0).unwrap()
    );
} 