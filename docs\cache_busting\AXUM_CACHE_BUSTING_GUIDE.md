# Axum Cache Busting - Comprehensive Guide

This guide covers all the server-side cache busting features implemented in your Axum backend.

## 🚀 Overview

Your Axum backend now includes comprehensive cache busting capabilities:

- **Smart Cache Headers**: Automatic cache control based on file types
- **Version Management**: Runtime cache version tracking
- **Asset Manifest**: Dynamic asset URL generation
- **API Endpoints**: REST endpoints for cache management
- **Middleware**: Automatic cache header injection
- **Client Integration**: JavaScript client for real-time updates

## 📋 Features Implemented

### 1. Enhanced Static File Service (`backend/src/static_files.rs`)

**Features:**
- Custom static file handler with security checks
- Automatic cache header generation based on file types
- ETag support for better caching
- Runtime cache version injection into HTML
- Asset manifest generation

**Cache Control Strategy:**
- **Versioned JS/CSS**: `max-age=2592000, immutable` (30 days)
- **Non-versioned JS/CSS**: `max-age=300` (5 minutes)
- **HTML files**: `max-age=0, must-revalidate` (no caching)
- **Other assets**: `max-age=86400` (1 day)

### 2. Cache Busting Middleware (`backend/src/cache_middleware.rs`)

**Features:**
- Automatic cache header injection
- Version tracking and management
- Asset manifest generation and updates
- Development-friendly cache refresh endpoints

**State Management:**
- Thread-safe cache version storage
- Dynamic asset manifest updates
- Runtime asset information tracking

### 3. API Endpoints

| Endpoint | Method | Description |
|----------|--------|-------------|
| `/api/cache/version` | GET | Get current cache version and asset info |
| `/api/cache/manifest` | GET | Get asset manifest for cache busting |
| `/api/cache/refresh` | GET | Refresh cache version (development) |
| `/api/cache/health` | GET | Health check with cache information |

### 4. Client-Side Integration (`frontend/cache-client.js`)

**Features:**
- Automatic version checking
- Real-time update notifications
- Dynamic script/CSS loading with cache busting
- Development tools integration

## 🔧 Configuration

### Environment Variables

| Variable | Description | Default |
|----------|-------------|---------|
| `CACHE_VERSION` | Cache version for busting | Current timestamp |
| `ENVIRONMENT` | Runtime environment | development |

### Docker Integration

```dockerfile
# Set cache version during build
ARG CACHE_VERSION=""
ENV CACHE_VERSION=$CACHE_VERSION
```

## 🛠️ Usage

### Basic Setup

Your Axum server automatically initializes cache busting:

```rust
// Cache version from environment or timestamp
let cache_version = std::env::var("CACHE_VERSION").ok();
let cache_state = CacheBustingState::new(cache_version);
```

### API Usage

#### Get Cache Version Information

```bash
curl http://localhost:8000/api/cache/version
```

Response:
```json
{
  "version": "**********",
  "timestamp": **********,
  "assets": {
    "app.js": {
      "name": "app.js",
      "url": "app.js?v=**********",
      "version": "**********",
      "size": 1024,
      "content_type": "application/javascript",
      "last_modified": **********
    }
  }
}
```

#### Get Asset Manifest

```bash
curl http://localhost:8000/api/cache/manifest
```

Response:
```json
{
  "version": "**********",
  "assets": {
    "app.js": "app.js?v=**********",
    "style.css": "style.css?v=**********"
  }
}
```

#### Refresh Cache (Development)

```bash
curl http://localhost:8000/api/cache/refresh
```

Response:
```json
{
  "status": "success",
  "message": "Cache refreshed",
  "new_version": "1701431345",
  "timestamp": 1701431345
}
```

### Client-Side Usage

#### Basic Integration

```javascript
// Auto-initialized by default
// Access via window.cacheBustingClient

// Manual initialization
const client = new CacheBustingClient();
await client.init({
    checkInterval: 30000, // 30 seconds
    autoRefresh: false    // Show notification instead
});
```

#### Dynamic Asset Loading

```javascript
// Load script with cache busting
await client.loadScript('modules/feature.js');

// Load CSS with cache busting
await client.loadCSS('styles/theme.css');

// Get asset URL with cache busting
const url = client.getAssetUrl('app.js');
```

#### Version Management

```javascript
// Get current version
const version = client.getCurrentVersion();

// Check for updates
await client.checkForUpdates();

// Refresh cache (development)
await client.refreshCache();
```

## 🎯 Development Workflow

### 1. Local Development

```bash
# Start server with cache busting
CACHE_VERSION=dev-$(date +%s) cargo run

# Or let it auto-generate
cargo run
```

### 2. Frontend Development

The enhanced HTML includes development controls:
- **Refresh Cache**: Manually refresh server cache
- **Check Updates**: Check for version updates
- **Show Cache Info**: Display cache information

### 3. Docker Development

```bash
# Build with cache busting
docker build --build-arg CACHE_VERSION=dev-$(date +%s) -t myapp .

# Run with cache busting
docker run -e CACHE_VERSION=dev-$(date +%s) myapp
```

## 📊 Monitoring and Debugging

### Cache Headers

All responses include cache busting headers:
- `X-Cache-Version`: Current cache version
- `Cache-Control`: Appropriate cache control
- `ETag`: Version-based ETag

### Development Tools

#### Browser Console

```javascript
// Access cache client
window.cacheBustingClient.getCurrentVersion()
window.cacheBustingClient.getAssetManifest()

// Check for updates
await window.cacheBustingClient.checkForUpdates()
```

#### Server Logs

The server logs cache version on startup:
```
🚀 Server starting on http://0.0.0.0:8000
🔧 Cache version: **********
📊 Cache busting endpoints:
  - GET /api/cache/version    - Get current cache version and assets
  - GET /api/cache/manifest   - Get asset manifest
  - GET /api/cache/refresh    - Refresh cache (development)
  - GET /api/cache/health     - Health check with cache info
```

## 🔄 Cache Invalidation Strategies

### 1. Automatic Invalidation

- **Build-time**: Docker builds inject new cache versions
- **Runtime**: Development refresh endpoint generates new versions
- **Client-side**: Automatic detection of version changes

### 2. Manual Invalidation

```bash
# Refresh cache via API
curl http://localhost:8000/api/cache/refresh

# Or use client-side controls
cacheBustingClient.refreshCache()
```

### 3. Deployment Invalidation

```bash
# Set version during deployment
export CACHE_VERSION="1.2.3"
# or
export CACHE_VERSION="$(git rev-parse HEAD)"
```

## 🛡️ Security Considerations

### Path Traversal Protection

```rust
// Security check in static file handler
if !file_path.starts_with("./static") {
    return (StatusCode::FORBIDDEN, "Access denied").into_response();
}
```

### Safe Headers

- No sensitive information in cache headers
- Version information is safe to expose
- Build arguments don't persist in image layers

## 🚀 Production Deployment

### 1. Environment Setup

```bash
# Set production cache version
export CACHE_VERSION="1.2.3"
# or use git commit hash
export CACHE_VERSION="$(git rev-parse HEAD)"
```

### 2. Docker Production

```dockerfile
# Multi-stage build with cache busting
FROM rust:alpine AS builder
# ... build steps ...

FROM alpine:latest
COPY --from=builder /app/target/release/app /app
ENV CACHE_VERSION=1.2.3
CMD ["/app"]
```

### 3. Kubernetes Deployment

```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: myapp
spec:
  template:
    spec:
      containers:
      - name: app
        image: myapp:1.2.3
        env:
        - name: CACHE_VERSION
          value: "1.2.3"
```

## 🔧 Advanced Configuration

### Custom Cache Headers

```rust
// Extend cache_middleware.rs
pub fn custom_cache_headers(file_type: &str) -> HeaderMap {
    let mut headers = HeaderMap::new();
    
    match file_type {
        "js" | "css" => {
            headers.insert(
                header::CACHE_CONTROL,
                HeaderValue::from_static("public, max-age=2592000, immutable"),
            );
        }
        "html" => {
            headers.insert(
                header::CACHE_CONTROL,
                HeaderValue::from_static("public, max-age=0, must-revalidate"),
            );
        }
        _ => {
            headers.insert(
                header::CACHE_CONTROL,
                HeaderValue::from_static("public, max-age=86400"),
            );
        }
    }
    
    headers
}
```

### Custom Asset Manifest

```rust
// Extend static_files.rs
pub fn generate_custom_manifest() -> HashMap<String, AssetInfo> {
    let mut manifest = HashMap::new();
    
    // Add custom asset processing logic
    if let Ok(entries) = fs::read_dir("./static") {
        for entry in entries.flatten() {
            // Custom processing per file type
            let asset_info = process_asset(&entry);
            manifest.insert(entry.file_name().to_string(), asset_info);
        }
    }
    
    manifest
}
```

## 📈 Performance Impact

### Server-Side

- **Minimal CPU overhead**: Cache headers generation is lightweight
- **Memory usage**: Asset manifest stored in memory (~KB)
- **Network**: Additional headers add ~100 bytes per response

### Client-Side

- **Polling**: 30-second intervals for version checks
- **Memory**: ~10KB for cache client
- **Network**: Periodic lightweight API calls

## 🤝 Integration Examples

### CI/CD Pipeline

```yaml
# GitHub Actions
- name: Build with cache busting
  run: |
    export CACHE_VERSION="${{ github.sha }}"
    docker build --build-arg CACHE_VERSION="$CACHE_VERSION" -t myapp .
```

### Load Balancer Configuration

```nginx
# Nginx configuration
location ~* \.(js|css)$ {
    # Let application handle cache headers
    proxy_pass http://backend;
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
}
```

## 🎉 Benefits

1. **Automatic**: No manual cache version management
2. **Flexible**: Works with any deployment strategy
3. **Real-time**: Client-side update detection
4. **Secure**: No sensitive information exposure
5. **Performant**: Minimal overhead
6. **Developer-friendly**: Rich development tools
7. **Production-ready**: Robust error handling

## 🔮 Future Enhancements

1. **Service Worker Integration**: Advanced caching strategies
2. **CDN Integration**: Cache invalidation at CDN level
3. **Asset Optimization**: Automatic compression and optimization
4. **Metrics Collection**: Cache hit/miss statistics
5. **A/B Testing**: Version-based feature flags 