#!/bin/bash

# Docker build script with cache busting support
set -e

# Default values
TAG="myatm-embedded-ui:latest"
VERSION=""
REGISTRY=""
PUSH=false
NO_CACHE=false

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -t|--tag)
            TAG="$2"
            shift 2
            ;;
        -v|--version)
            VERSION="$2"
            shift 2
            ;;
        -r|--registry)
            REGISTRY="$2"
            shift 2
            ;;
        --push)
            PUSH=true
            shift
            ;;
        --no-cache)
            NO_CACHE=true
            shift
            ;;
        -h|--help)
            echo "Usage: $0 [OPTIONS]"
            echo "Options:"
            echo "  -t, --tag TAG          Docker image tag (default: myatm-embedded-ui:latest)"
            echo "  -v, --version VERSION  Cache version (default: timestamp)"
            echo "  -r, --registry REGISTRY Registry URL for tagging and pushing"
            echo "  --push                 Push to registry after build"
            echo "  --no-cache             Build without using cache"
            echo "  -h, --help             Show this help message"
            exit 0
            ;;
        *)
            echo "Unknown option: $1"
            exit 1
            ;;
    esac
done

echo "🐳 Building Docker image with cache busting..."

# Generate version if not provided
if [ -z "$VERSION" ]; then
    VERSION=$(date +%Y%m%d%H%M%S)
fi

echo "📦 Using cache version: $VERSION"
echo "🏷️  Building image: $TAG"

# Build Docker image with cache version
BUILD_ARGS=(
    "--build-arg" "CACHE_VERSION=$VERSION"
)

if [ "$NO_CACHE" = true ]; then
    BUILD_ARGS+=("--no-cache")
fi

BUILD_ARGS+=("-t" "$TAG" ".")

echo "🔨 Running: docker build ${BUILD_ARGS[*]}"

if docker build "${BUILD_ARGS[@]}"; then
    echo "✅ Docker build completed successfully!"
    echo "📋 Image: $TAG"
    echo "🏷️  Cache Version: $VERSION"
    
    # Tag with registry if provided
    if [ -n "$REGISTRY" ]; then
        REGISTRY_TAG="$REGISTRY/$TAG"
        echo "🏷️  Tagging for registry: $REGISTRY_TAG"
        docker tag "$TAG" "$REGISTRY_TAG"
        
        if [ "$PUSH" = true ]; then
            echo "📤 Pushing to registry..."
            if docker push "$REGISTRY_TAG"; then
                echo "✅ Push completed successfully!"
            else
                echo "❌ Push failed!"
                exit 1
            fi
        fi
    fi
    
    echo "🚀 Build process completed!"
else
    echo "❌ Build failed!"
    exit 1
fi 