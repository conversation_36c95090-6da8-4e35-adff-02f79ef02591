# Run script for Rust BFF Docker container
param(
    [switch]$Build,
    [switch]$Stop
)

$containerName = "rust-bff-app"
$imageName = "rust-bff-app"
$port = "8080"

if ($Stop) {
    Write-Host "🛑 Stopping and removing container..." -ForegroundColor Yellow
    docker stop $containerName 2>$null
    docker rm $containerName 2>$null
    Write-Host "✅ Container stopped and removed" -ForegroundColor Green
    exit 0
}

if ($Build) {
    Write-Host "🔨 Building Docker image..." -ForegroundColor Yellow
    docker build -t $imageName .
    if ($LASTEXITCODE -ne 0) {
        Write-Host "❌ Build failed!" -ForegroundColor Red
        exit 1
    }
}

# Stop existing container if running
docker stop $containerName 2>$null | Out-Null
docker rm $containerName 2>$null | Out-Null

Write-Host "🚀 Starting container '$containerName'..." -ForegroundColor Cyan
docker run -d -p "${port}:${port}" --name $containerName $imageName

if ($LASTEXITCODE -eq 0) {
    Start-Sleep 2
    Write-Host "✅ Container started successfully!" -ForegroundColor Green
    Write-Host "📍 Application: http://localhost:$port" -ForegroundColor Cyan
    Write-Host "🔗 API: http://localhost:$port/api/data" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "To stop: .\run.ps1 -Stop" -ForegroundColor Gray
    Write-Host "To rebuild: .\run.ps1 -Build" -ForegroundColor Gray
} else {
    Write-Host "❌ Failed to start container!" -ForegroundColor Red
    exit 1
} 