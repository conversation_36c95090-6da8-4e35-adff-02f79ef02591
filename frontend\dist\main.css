/* src/styles.css */
body {
  font-family: sans-serif;
  margin-top: 2px;
  margin-left: 5px;
  margin-right: 5px;
  margin-bottom: 0px;
}
#Grid {
  max-width: 900px;
  margin: auto;
}
.cache-notification {
  position: fixed;
  top: 20px;
  right: 20px;
  background: #4CAF50;
  color: white;
  padding: 12px 20px;
  border-radius: 6px;
  font-size: 14px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 10000;
  opacity: 0;
  transform: translateX(100%);
  transition: all 0.3s ease-in-out;
  max-width: 300px;
  display: none;
}
.cache-notification.show {
  opacity: 1;
  transform: translateX(0);
  display: block;
}
.cache-notification.updating {
  background: #2196F3;
}
.cache-notification.error {
  background: #f44336;
}
.dev-controls {
  position: fixed;
  bottom: 10px;
  right: 10px;
  background: #007bff;
  color: white;
  padding: 10px;
  border-radius: 5px;
  font-size: 12px;
  z-index: 1000;
}
.dev-controls button {
  background: white;
  color: #007bff;
  border: none;
  padding: 5px 10px;
  margin: 2px;
  border-radius: 3px;
  cursor: pointer;
  font-size: 11px;
}
.dev-controls button:hover {
  background: #f0f0f0;
}
@media (min-width: 1px) {
  .dev-controls[data-production=true] {
    display: none;
  }
}
/*# sourceMappingURL=main.css.map */
