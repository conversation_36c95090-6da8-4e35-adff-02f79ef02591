// # deposits/mod.rs
//
// Deposits Module - Modular Deposits Functionality
//
// This module encapsulates all deposits-related functionality in a clean, modular design.
// It provides a Backend-for-Frontend (BFF) layer for handling deposit data operations.
//
// ## Module Structure
// - `config` - Configuration management for deposits functionality
// - `handlers` - HTTP request handlers for deposits API endpoints
// - `odata` - OData v4 query parsing and processing utilities
//
// ## Public API
// The module re-exports `DepositsConfig` for easy access by the main application configuration.

pub mod config;
pub mod handlers;
pub mod odata;

// Re-export commonly used types
pub use config::DepositsConfig; 