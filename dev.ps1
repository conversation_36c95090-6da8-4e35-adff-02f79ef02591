# Development script for Rust BFF Application
Write-Host "🔧 Setting up development environment..." -ForegroundColor Cyan

# Build frontend with bundling
Write-Host "🔨 Building frontend..." -ForegroundColor Yellow
& .\build-frontend.ps1 -Dev

Write-Host "✅ Frontend built and deployed to backend/static/" -ForegroundColor Green

# Start the Rust backend server
Write-Host "🚀 Starting Rust backend server..." -ForegroundColor Yellow
Set-Location "backend"
Write-Host "📍 Server will be available at: http://localhost:8000" -ForegroundColor Cyan
Write-Host "🔗 API endpoint: http://localhost:8000/api/data" -ForegroundColor Cyan
Write-Host ""
Write-Host "Press Ctrl+C to stop the server" -ForegroundColor Gray

cargo run 