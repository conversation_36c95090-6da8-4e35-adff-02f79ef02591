# Cache Busting Guide

This guide explains various approaches to handle browser cache invalidation for JavaScript files in your application.

## Problem

When browsers cache JavaScript files, they might serve stale versions even after you've updated the code. This leads to users seeing outdated functionality until they manually clear their browser cache.

## Solutions Implemented

### 1. Query Parameter Cache Busting (Recommended)

The simplest approach is to add a version parameter to your JavaScript file URLs.

**Current Implementation:**
```html
<script src="app.js?v=1.0.0"></script>
```

**Manual Update:**
- Change the version number in your HTML files whenever you update JavaScript
- Update both `frontend/index.html` and `backend/static/index.html`

### 2. Automated Scripts

#### build-frontend.ps1
Complete build script that copies files and updates cache versions automatically.

```powershell
# Use with timestamp (automatic)
./build-frontend.ps1

# Use with specific version
./build-frontend.ps1 -Version "1.2.3"
```

#### update-cache-version.ps1
Simple script to update cache versions without copying files.

```powershell
# Use with timestamp (automatic)
./update-cache-version.ps1

# Use with specific version
./update-cache-version.ps1 -Version "1.2.3"
```

### 3. Dynamic JavaScript Loading

For advanced scenarios, use `cache-utils.js` for dynamic script loading:

```javascript
// Load a single script with cache busting
loadScript('modules/feature.js').then(() => {
    console.log('Script loaded with cache busting');
});

// Load multiple scripts
loadScripts(['utils.js', 'components.js'], '1.0.0');

// Reload all local scripts with new version
reloadScripts('1.0.1');
```

## Development Workflow

### Option A: Manual Version Management
1. Edit your JavaScript files in `frontend/`
2. Update version numbers in HTML files
3. Copy files to `backend/static/` or run the build script

### Option B: Automated Build Process
1. Edit your JavaScript files in `frontend/`
2. Run `./build-frontend.ps1` to copy files and update versions automatically
3. The script will use timestamp-based versioning

### Option C: Quick Version Update
1. After making changes, run `./update-cache-version.ps1`
2. This updates the version parameter in all HTML files

## Best Practices

1. **Use Semantic Versioning**: For production releases, use semantic versioning (e.g., 1.0.0, 1.0.1, 1.1.0)
2. **Use Timestamps for Development**: For development, timestamp-based versions work well
3. **Automate the Process**: Use the provided scripts to avoid manual errors
4. **Test After Updates**: Always test in an incognito/private browser window to verify cache busting works
5. **Document Version Changes**: Keep track of what changed in each version

## Adding New JavaScript Files

When adding new JavaScript files:

1. Add them to the `frontend/` directory
2. Include them in your HTML with version parameters:
   ```html
   <script src="new-feature.js?v=1.0.0"></script>
   ```
3. Update the build script if needed to copy new files
4. The version update scripts will automatically handle the new files

## Server-Side Considerations

The current Rust backend serves static files without special cache headers. For production, consider:

1. **HTTP Cache Headers**: Set appropriate `Cache-Control` headers
2. **ETags**: Use ETags for more sophisticated caching
3. **Content Hashing**: Consider using file content hashes instead of versions

## Testing Cache Busting

1. Open your application in a browser
2. Make a change to a JavaScript file
3. Update the version using one of the provided scripts
4. Hard refresh (Ctrl+F5) or open in incognito mode
5. Verify the new version is loaded by checking the Network tab in DevTools

## File Structure

```
├── frontend/                    # Source files for development
│   ├── index.html
│   └── app.js
├── backend/static/              # Files served by the backend
│   ├── index.html
│   └── app.js
├── build-frontend.ps1           # Complete build script
├── update-cache-version.ps1     # Version update script
└── cache-utils.js               # Dynamic loading utilities
```

## Future Enhancements

1. **Webpack/Vite Integration**: For larger projects, consider using a build tool that handles cache busting automatically
2. **Service Worker**: Implement a service worker for more control over caching
3. **CDN Integration**: If using a CDN, implement cache invalidation at the CDN level
4. **Automated CI/CD**: Integrate version updates into your deployment pipeline 