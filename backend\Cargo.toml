# Cargo.toml

[package]
name = "myatm-embedded-ui"
version = "0.1.0"
edition = "2024"
rust-version = "1.88"   # optional guard

[dependencies]
# Web framework
axum = "0.7"
tokio = { version = "1.0", features = ["full"] }
tower = "0.4"
tower-http = { version = "0.5", features = ["cors", "trace", "fs", "compression-gzip", "compression-br", "compression-zstd", "set-header", "timeout", "limit", "request-id", "add-extension"] }

# HTTP
hyper = "1.0"
reqwest = { version = "0.12", features = ["json", "rustls-tls"], default-features = false }

# Serialization
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
serde_urlencoded = "0.7"

# Database - using tiberius for SQL Server
# (Optional: Uncomment if needed)
# tiberius = { version = "0.12", features = ["chrono", "rust_decimal"] }
# tokio-util = { version = "0.7", features = ["compat"] }
# futures-util = "0.3"

# Date/Time
chrono = { version = "0.4", features = ["serde"] }

# Decimal handling
rust_decimal = { version = "1.32", features = ["serde"] }

# UUID support
uuid = { version = "1.0", features = ["v4", "serde"] }

# Configuration
config = "0.14"
dotenvy = "0.15"

# Elasticsearch for direct logging (compatible with ES 7.14+)
elasticsearch = "8.17.0-alpha.1"
base64 = "0.21"
url = "2.0"

# OpenTelemetry and Tracing
log = "0.4"
tracing = "0.1"
tracing-subscriber = { version = "0.3", features = ["env-filter", "fmt", "json"] }
tracing-opentelemetry = "0.21"
opentelemetry = "0.20"
opentelemetry_sdk = { version = "0.20", features = ["rt-tokio", "metrics"] }
opentelemetry-otlp = { version = "0.13", features = ["http-proto", "reqwest-client", "metrics"] }

# System metrics
sysinfo = "0.30"

# Error handling
anyhow = "1.0"
thiserror = "1.0"

# JWT handling
jsonwebtoken = "9.2"

# Regex for OData parsing
regex = "1.10"

# Environment
env_logger = "0.10"

# Performance and Security
headers = "0.4"
http-body-util = "0.1" 