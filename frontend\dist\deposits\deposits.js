// # deposits.js
//
// Deposits page module - vanilla JS port of Angular component
// This module handles the deposits page functionality including Syncfusion Grid,
// date/time filtering, grouping, aggregates, and export options.

import router from '../router.js';
import { registerSyncfusionLicense } from '../utils/syncfusion-utils.js';

// Import Syncfusion components
import { registerLicense } from '@syncfusion/ej2-base';
import { DataManager, ODataV4Adaptor, Query, Predicate } from '@syncfusion/ej2-data';
import { Grid, Group, Sort, Filter, Toolbar, ExcelExport, PdfExport, Aggregate, Page, Search, Resize } from '@syncfusion/ej2-grids';
import { DateRangePicker, TimePicker } from '@syncfusion/ej2-calendars';

// Inject Grid services
Grid.Inject(Group, Sort, Filter, Toolbar, ExcelExport, PdfExport, Aggregate, Page, Search, Resize);

// Custom V4 Adaptor for OData queries (ported from TypeScript)
class CustomV4Adaptor extends ODataV4Adaptor {
    processQuery() {
        const original = super.processQuery.apply(this, arguments);
        let url = original.url;
        
        console.log('=== CUSTOM V4 ADAPTOR DEBUG ===');
        console.log('Original URL from Syncfusion:', original.url);
        
        // Apply URL transformations from the original CustomV4Adaptor
        url = url.replace('/?', '?')
                 .replace("ge '", 'ge ')
                 .replace("' and DepositDateTime le", ' and DepositDateTime le')
                 .replace("le '", 'le ')
                 .replace("'&", '&')
                 .replace("00Z'", '00Z')
                 .replace("'or ((startswith", ' or ((startswith')
                 .replace(/\((TerminalId eq '[^']+')\)\s+or\s+\((TerminalId eq '[^']+')\)/, '($1 or $2)')
                 .replace(/\(\((TerminalId eq '[^']+' or TerminalId eq '[^']+')\)\)/, '($1)')
                 .replace(/\(\(DepositDateTime ge ([^)]*)\)\s+and\s+\(DepositDateTime le ([^)]*)\)\)/,
                          '(DepositDateTime ge $1 and DepositDateTime le $2)')
                 .replace(/(DepositDateTime le [0-9T:\-%\.Z]+)'/, '$1')
                 .replace(/\(DepositDateTime ge ([^']*) and DepositDateTime le ([^']*)'\)/,
                          '(DepositDateTime ge $1 and DepositDateTime le $2)')
                 .replace('DepositDateTimedesc', 'DepositDateTime desc')
                 .replace('SubmitSettlementDatedesc', 'SubmitSettlementDate desc');
        
        console.log('Final URL after transformations:', url);
        console.log('=== END CUSTOM V4 ADAPTOR DEBUG ===');
        
        original.url = url;
        return original;
    }
}

// Deposits module class
class DepositsModule {
    constructor() {
        this.grid = null;
        this.dateRangePicker = null;
        this.startTimePicker = null;
        this.endTimePicker = null;
        this.gridData = null;
        this.query = null;
        this.initialized = false;
        this.terminalId = null; // Store terminal ID from query params
        this.multiMerchant = false; // Flag for multi-merchant mode
        this.isMca = false; // Flag for MCA mode
        
        // Date constants
        this.today = new Date(new Date().toDateString());
        this.last7Start = new Date(this.today.getTime() - 7 * 24 * 60 * 60 * 1000);
        this.weekStart = this.getWeekStart();
        this.monthStart = this.getMonthStart();
        this.lastMonthStart = this.getLastMonthStart();
        this.lastMonthEnd = this.getLastMonthEnd();
        
        // Default date range (last 30 days)
        const defaultStartDate = new Date(this.today.getTime() - 30 * 24 * 60 * 60 * 1000);
        this.dateStart = new Date(defaultStartDate);
        this.dateStart.setHours(0, 0, 0, 0); // Set to start of day
        
        this.dateEnd = new Date(this.today);
        this.dateEnd.setHours(23, 59, 59, 999); // Set to end of day
        
        // Grid configuration
        this.sortOptions = { allowUnsort: false };
        this.searchOptions = { operator: 'contains', ignoreCase: true };
        this.groupSettings = { showDropArea: false, columns: ['SubmitSettlementDate'] };
        this.toolbar = ['Search', 'ExcelExport', 'PdfExport'];
        this.formatString = 'HH:mm';
        this.interval = 60;
        
        // Format options
        this.dateFormatOptions = { type: 'date', format: 'dd-MMM-yyyy' };
        this.dateFormatTimeOptions = { type: 'dateTime', format: 'dd-MMM-yyyy HH:mm' };
        
        // Aggregates configuration - will be set dynamically based on mode
        this.aggregates = this.getGridAggregates();
    }


    // Initialize the deposits page
    async init() {
        if (this.initialized) {
            return;
        }

        console.log('Initializing deposits page');
        
        try {
            // Register Syncfusion license first
            await registerSyncfusionLicense();
            // Check for terminalId and multiMerchant in query parameters
            this.checkTerminalIdFromQueryParams();
            if (this.multiMerchant) {
                console.log('Multi-merchant mode enabled');
            }
            
            // Load the HTML template
            await this.loadTemplate();
            
            // Give the DOM a moment to update after innerHTML change
            await new Promise(resolve => setTimeout(resolve, 100));
            
            // Initialize Syncfusion controls
            this.initializeDateControls();
            this.initializeGrid();
            
            // Note: Initial data loading is now handled by the grid's 'created' event
            
            this.initialized = true;
            console.log('Deposits page initialized successfully');
            
            // Show terminal ID info if present
            if (this.terminalId) {
                console.log(`Filtering deposits for terminal ID: ${this.terminalId}`);
                this.showTerminalIdInfo();
            }
        } catch (error) {
            console.error('Failed to initialize deposits page:', error);
            this.showError('Failed to initialize deposits page: ' + error.message);
        }
    }

    // Check for terminalId, multiMerchant, and mca in query parameters
    checkTerminalIdFromQueryParams() {
        // First try router if available and has parsed query params
        if (typeof router !== 'undefined' && router.getQueryParam) {
            this.terminalId = router.getQueryParam('terminalId');
            this.multiMerchant = router.getQueryParam('multiMerchant') === 'true';
            this.isMca = router.getQueryParam('mca') === 'true';
            if (this.terminalId) {
                console.log(`Found terminalId in query params via router: ${this.terminalId}`);
            }
            if (this.multiMerchant) {
                console.log(`Found multiMerchant in query params via router: ${this.multiMerchant}`);
            }
            if (this.isMca) {
                console.log(`Found mca in query params via router: ${this.isMca}`);
            }
            // If we got at least one parameter from router, we're done
            if (this.terminalId || this.multiMerchant || this.isMca) {
                return;
            }
        }
        
        // Fallback: parse URL directly
        try {
            const urlParams = new URLSearchParams(window.location.search);
            this.terminalId = urlParams.get('terminalId');
            this.multiMerchant = urlParams.get('multiMerchant') === 'true';
            this.isMca = urlParams.get('mca') === 'true';
            if (this.terminalId) {
                console.log(`Found terminalId in query params via direct URL parsing: ${this.terminalId}`);
            }
            if (this.multiMerchant) {
                console.log(`Found multiMerchant in query params via direct URL parsing: ${this.multiMerchant}`);
            }
            if (this.isMca) {
                console.log(`Found mca in query params via direct URL parsing: ${this.isMca}`);
            }
        } catch (error) {
            console.error('Error parsing URL parameters:', error);
        }
    }

    // Show terminal ID info to user
    showTerminalIdInfo() {
        // You could add a visual indicator here if needed
        console.log(`Deposits filtered for Terminal ID: ${this.terminalId}`);
    }

    // Load the HTML template
    async loadTemplate() {
        try {
            console.log('Starting template fetch...');
            const response = await fetch('/static/deposits/deposits.html');
            console.log('Template fetch response:', response.status, response.statusText);
            
            if (!response.ok) {
                throw new Error(`Failed to load template: ${response.status} ${response.statusText}`);
            }
            
            const html = await response.text();
            console.log('Template HTML received, length:', html.length);
            console.log('Template HTML preview:', html.substring(0, 200) + '...');
            
            // Insert the template into the main content area
            const mainContent = document.getElementById('main-content');
            console.log('Main content element before insertion:', mainContent);
            
            if (!mainContent) {
                throw new Error('Main content element (#main-content) not found!');
            }
            
            console.log('Current main content innerHTML before replacement:', mainContent.innerHTML.substring(0, 100));
            mainContent.innerHTML = html;
            console.log('Template inserted into main content');
            console.log('New main content innerHTML after replacement:', mainContent.innerHTML.substring(0, 200) + '...');

            // Dynamically inject deposits.css with cache busting if not already present
            if (window.cacheBustingClient && !document.getElementById('deposits-css-dynamic')) {
                await window.cacheBustingClient.loadCSS('/static/deposits/deposits.css', { id: 'deposits-css-dynamic' });
            }
            // Dynamically inject deposits.js with cache busting if not already present
            if (window.cacheBustingClient && !document.getElementById('deposits-js-dynamic')) {
                await window.cacheBustingClient.loadScript('/static/deposits/deposits.js', { id: 'deposits-js-dynamic' });
            }
            
            // Verify the grid container exists after DOM update
            const gridContainer = document.getElementById('deposits-grid');
            console.log('Grid container search result:', !!gridContainer, gridContainer);
            
            if (!gridContainer) {
                console.error('Grid container still not found after template insertion!');
                console.log('All elements with id containing "grid":', 
                    document.querySelectorAll('[id*="grid"]'));
                console.log('Current DOM structure of main-content:', mainContent.innerHTML);
            }
            
        } catch (error) {
            console.error('Error loading deposits template:', error);
            throw error;
        }
    }

    // Initialize date controls
    initializeDateControls() {
        console.log('Initializing date controls');
        
        // Date range picker
        this.dateRangePicker = new DateRangePicker({
            strictMode: true,
            width: '200px',
            format: 'yyyy/MM/dd',
            startDate: this.dateStart,
            endDate: this.dateEnd,
            presets: [
                { label: 'Today', start: this.setToStartOfDay(this.today), end: this.setToEndOfDay(this.today) },
                { label: 'Last 7 Days', start: this.setToStartOfDay(this.last7Start), end: this.setToEndOfDay(this.today) },
                { label: 'Current Week', start: this.setToStartOfDay(this.weekStart), end: this.setToEndOfDay(this.today) },
                { label: 'Current Month', start: this.setToStartOfDay(this.monthStart), end: this.setToEndOfDay(this.today) },
                { label: 'Last Month', start: this.setToStartOfDay(this.lastMonthStart), end: this.setToEndOfDay(this.lastMonthEnd) }
            ],
            change: (event) => this.onDateSelect(event)
        });
        this.dateRangePicker.appendTo('#drpDate');

        // Start time picker
        this.startTimePicker = new TimePicker({
            format: this.formatString,
            step: this.interval,
            change: (event) => this.onEnableEndTime(event)
        });
        this.startTimePicker.appendTo('#startPicker');

        // End time picker
        this.endTimePicker = new TimePicker({
            enabled: false,
            format: this.formatString,
            step: this.interval,
            change: (event) => this.onSearchWithTime(event)
        });
        this.endTimePicker.appendTo('#endPicker');
    }

    // Initialize the Syncfusion Grid
    initializeGrid() {
        console.log('Initializing Syncfusion Grid');
        
        // Check if grid container exists before creating grid
        const gridContainer = document.getElementById('deposits-grid');
        if (!gridContainer) {
            console.error('Grid container #deposits-grid not found in DOM!');
            this.showError('Grid container not found. Template may not have loaded properly.');
            return;
        }
        
        console.log('Grid container found, proceeding with grid creation');
        
        // Construct data source URL with terminalId, multiMerchant, and mca if present
        const parts = [];
        if (this.terminalId)    parts.push(`terminalId=${encodeURIComponent(this.terminalId)}`);
        if (this.multiMerchant) parts.push('multiMerchant=true');
        if (this.isMca)         parts.push('mca=true');
        const qs = parts.length ? '?' + parts.join('&') : '';
        let dataSourceUrl = '/api/deposits' + qs;
        console.log(`Data source URL with terminalId, multiMerchant, and mca: ${dataSourceUrl}`);
        
        // Create data manager with custom adaptor
        this.gridData = new DataManager({
            url: dataSourceUrl,
            adaptor: new CustomV4Adaptor(),
            crossDomain: true
        });

        // Set up the proper query with filters BEFORE creating the grid to prevent double requests
        this.setupInitialQuery();

        console.log('Creating Syncfusion Grid object...');

        // Grid configuration
        this.grid = new Grid({
            dataSource: this.gridData,
            query: this.query,
            allowSorting: true,
            allowResizing: true,
            allowExcelExport: true,
            allowPdfExport: true,
            allowGrouping: true,
            allowPaging: false,
            gridLines: 'Both',
            toolbar: this.toolbar,
            sortSettings: this.sortOptions,
            searchSettings: this.searchOptions,
            groupSettings: this.groupSettings,
            aggregates: this.getGridAggregates(),
            noRecordsTemplate: `
                <div style="text-align: center; padding: 40px 20px; color: #666;">
                    <div style="font-size: 48px; margin-bottom: 16px;">📊</div>
                    <h3 style="margin: 0 0 8px 0; color: #495057;">No Deposits Found</h3>
                    <p style="margin: 0; font-size: 14px;">
                        No deposits match your current filter criteria.
                        ${this.terminalId ? `<br>Terminal ID: ${this.terminalId}` : ''}
                    </p>
                    <p style="margin: 8px 0 0 0; font-size: 12px; color: #868e96;">
                        Try adjusting your date range or search filters.
                    </p>
                </div>
            `,
            columns: this.getGridColumns(),
            toolbarClick: (args) => this.toolbarClick(args),
            actionFailure: (args) => this.onGridFailure(args),
            beforeDataBound: () => this.beforeDataBound(),
            dataBound: () => this.dataBound(),
            load: () => this.load(),
            created: () => this.onGridCreated(),
            exportGroupCaption: (args) => this.exportGroupCaption(args)
        });

        console.log('Grid object created, appending to DOM...');
        try {
            this.grid.appendTo('#deposits-grid');
            console.log('Grid successfully appended to DOM');
        } catch (error) {
            console.error('Error appending grid to DOM:', error);
            this.showError('Failed to create grid: ' + error.message);
        }
    }

    // Get grid columns based on multiMerchant and MCA mode
    getGridColumns() {
        const baseColumns = [
            { field: 'RecordGuid', visible: false, isPrimaryKey: true },
            { field: 'SubmitSettlementDate', headerText: 'Settlement Date', allowSorting: false, format: this.dateFormatOptions, textAlign: 'Center', width: 150 },
            { field: 'DepositStatus', headerText: '', width: 40, allowSorting: false, allowFiltering: false, template: (data) => this.depositorGridIndicator(data.DepositorCode) }
        ];

        let dataColumns;
        
        if (this.isMca) {
            // MCA mode columns - multiMerchant wins if both flags are present
            dataColumns = [
                { field: 'DepositDateTime',        headerText: 'Deposit Date',             format: this.dateFormatTimeOptions, textAlign: 'Center', width: 100 },
                { field: 'TerminalId',             headerText: 'Terminal ID',              textAlign: 'Center', width: 60 },
                { field: 'DepositorName',          headerText: 'Depositor Name',           textAlign: 'Left',   width: 200 },
                { field: 'TraceNr',                headerText: 'Trace No',                 textAlign: 'Center', width: 70 },
                { field: 'CashDeposited',          headerText: 'Deposited',                textAlign: 'Right',  width: 120 },
                { field: 'AdvanceDeductedAmount',  headerText: 'Capital Express Deduction', textAlign: 'Right', width: 160, template: (data) => this.mcaDeductionTemplate(data) },
                { field: 'AdvanceSettlementAmount', headerText: 'Net Settlement',           textAlign: 'Right',  width: 120, template: (data) => this.mcaNetSettlementTemplate(data) }
            ];
        } else if (this.multiMerchant) {
            // Multi-merchant columns
            dataColumns = [
                { field: 'DepositDateTime',    headerText: 'Deposit Date',      format: this.dateFormatTimeOptions, textAlign: 'Centre', width: 180 },
                { field: 'TerminalId',         headerText: 'Terminal ID',       textAlign: 'Centre', width: 60 },
                { field: 'EntityTerminalId',   headerText: 'Sub Terminal ID',   textAlign: 'Centre', width: 150 },
                { field: 'DepositorName',      headerText: 'Depositor Name',    textAlign: 'Left',   width: 200 },
                { field: 'TraceNr',            headerText: 'Trace No',          textAlign: 'Centre', width: 70 },
                { field: 'EntityName',         headerText: 'Business Entity ID',textAlign: 'Left',   width: 200 },
                { field: 'CashDeposited',      headerText: 'Deposited',         textAlign: 'Right',  width: 120 },
                { field: 'SettledAmount',      headerText: 'Settled',           textAlign: 'Right',  width: 120 }
            ];
        } else {
            // Default columns for non-multi-merchant mode
            dataColumns = [
                { field: 'DepositDateTime', headerText: 'Deposit Date', format: this.dateFormatTimeOptions, allowSorting: false, textAlign: 'Center', width: 150 },
                { field: 'TerminalId', headerText: 'Terminal ID', textAlign: 'Center', allowSorting: false, allowFiltering: false, width: 60 },
                { field: 'DepositorName', headerText: 'Depositor Name', textAlign: 'Left', allowSorting: false, allowFiltering: false, width: 200 },
                { field: 'TraceNr', headerText: 'Trace No', textAlign: 'Center', allowSorting: false, allowFiltering: false, width: 70 },
                { field: 'CashDeposited', headerText: 'Deposited', textAlign: 'Right', format: 'N0', allowSorting: false, allowFiltering: false, width: 120 },
                { field: 'SettledAmount', headerText: 'Settled', textAlign: 'Right', format: 'N0', allowSorting: false, allowFiltering: false, width: 120 }
            ];
        }

        const endColumns = [
            { field: 'AmountStatus', headerText: '', width: 40, allowSorting: false, allowFiltering: false, template: (data) => this.isMca ? '' : this.gridAmountIndicator(data.SettledAmount, data.CashDeposited) }
        ];

        return [...baseColumns, ...dataColumns, ...endColumns];
    }

    // Get grid aggregates based on mode
    getGridAggregates() {
        if (this.isMca) {
            // MCA mode aggregates
            return [{
                columns: [
                    {
                        type: 'Sum',
                        field: 'CashDeposited',
                        format: 'N0',
                        groupCaptionTemplate: 'Total Deposited: ${Sum}'
                    },
                    {
                        type: 'Max',
                        field: 'AdvanceDeductedAmount',
                        format: 'N0',
                        groupCaptionTemplate: 'Capital Express Deduction: ${Max}'
                    },
                    {
                        type: 'Max',
                        field: 'AdvanceSettlementAmount',
                        format: 'N0',
                        groupCaptionTemplate: 'Total Net Settlement: ${Max}'
                    },
                    {
                        type: 'Custom',
                        customAggregate: this.customMcaValidation.bind(this),
                        groupCaptionTemplate: this.mcaIndicatorTemplate.bind(this),
                        field: 'AmountStatus'
                    }
                ]
            }];
        } else {
            // Default aggregates for non-MCA mode
            return [{
                columns: [
                    {
                        type: 'Sum',
                        field: 'CashDeposited',
                        format: 'N0',
                        groupCaptionTemplate: 'Total Deposited: ${Sum}'
                    },
                    {
                        type: 'Sum',
                        field: 'SettledAmount',
                        format: 'N0',
                        groupCaptionTemplate: 'Total Settled: ${Sum}'
                    }
                ]
            }];
        }
    }

    // Set up the initial query with date filters before grid creation
    setupInitialQuery() {
        console.log('Setting up initial query with date range:', this.dateStart, this.dateEnd);
        if (this.terminalId) {
            console.log('Including terminalId filter:', this.terminalId);
        }
        
        try {
            // Add date range filter using Syncfusion Predicate objects
            const startDate = this.formatDateForOData(this.dateStart);
            const endDate = this.formatDateForOData(this.dateEnd);
            
            console.log('Formatted dates for OData:', { startDate, endDate });
            
            // Create predicate-based filter
            let predicate = new Predicate('DepositDateTime', 'greaterthanorequal', startDate)
                .and('DepositDateTime', 'lessthanorequal', endDate);
            
            // Add terminal ID filter if present
            if (this.terminalId) {
                predicate = predicate.and('TerminalId', 'equal', this.terminalId);
                console.log(`Added terminalId filter: ${this.terminalId}`);
            }
            
            console.log('Created initial predicate filter successfully');
            
            // Create query with predicate filter
            this.query = new Query()
                .where(predicate)
                .sortBy('SubmitSettlementDate', 'descending')
                .sortBy('DepositDateTime', 'descending');

        } catch (error) {
            console.error('Error setting up initial query:', error);
            // Fallback to basic query
            this.query = new Query();
        }
    }

    // Date picker change event
    onDateSelect(event) {
        console.log('=== DATE RANGE SELECTION DEBUG ===');
        console.log('Date range selected:', event.startDate, event.endDate);
        
        // Set start date to beginning of day and end date to end of day
        this.dateStart = this.setToStartOfDay(event.startDate);
        this.dateEnd = this.setToEndOfDay(event.endDate);
        
        console.log('Adjusted date range:', this.dateStart, this.dateEnd);
        console.log('Formatted for OData:', {
            start: this.formatDateForOData(this.dateStart),
            end: this.formatDateForOData(this.dateEnd)
        });
        console.log('=== END DATE RANGE SELECTION DEBUG ===');
        this.refreshGrid();
    }

    // Start time picker change event
    onEnableEndTime(event) {
        console.log('Start time selected:', event.value);
        if (event.value) {
            this.endTimePicker.enabled = true;
        }
    }

    // End time picker change event
    onSearchWithTime(event) {
        console.log('End time selected:', event.value);
        if (event.value) {
            this.refreshByTime();
        }
    }

    // Refresh grid with current filters
    refreshGrid() {
        console.log('=== REFRESH GRID DEBUG ===');
        console.log('Refreshing grid with date range:', this.dateStart, this.dateEnd);
        if (this.terminalId) {
            console.log('Including terminalId filter:', this.terminalId);
        }
        
        if (!this.grid || !this.dateStart || !this.dateEnd) {
            console.log('Missing required components:', {
                grid: !!this.grid,
                dateStart: !!this.dateStart,
                dateEnd: !!this.dateEnd
            });
            this.hideLoading();
            return;
        }

        this.showLoading();

        try {
            // Debug logging
            console.log('Date objects before formatting:', {
                dateStart: this.dateStart,
                dateEnd: this.dateEnd,
                dateStartType: typeof this.dateStart,
                dateEndType: typeof this.dateEnd,
                dateStartValid: this.dateStart instanceof Date,
                dateEndValid: this.dateEnd instanceof Date,
                terminalId: this.terminalId
            });
            
            // Add date range filter using Syncfusion Predicate objects
            const startDate = this.formatDateForOData(this.dateStart);
            const endDate = this.formatDateForOData(this.dateEnd);
            
            console.log('Formatted dates for OData (refreshGrid):', { startDate, endDate });
            
            // Create predicate-based filter instead of raw string to avoid toLowerCase errors
            let predicate = new Predicate('DepositDateTime', 'greaterthanorequal', startDate)
                .and('DepositDateTime', 'lessthanorequal', endDate);
            
            // Add terminal ID filter if present
            if (this.terminalId) {
                predicate = predicate.and('TerminalId', 'equal', this.terminalId);
                console.log(`Added terminalId filter: ${this.terminalId}`);
            }
            
            console.log('Created predicate filter for refreshGrid:', predicate);
            
            // Create new query with predicate filter
            this.query = new Query()
                .where(predicate)
                .sortBy('SubmitSettlementDate', 'descending')
                .sortBy('DepositDateTime', 'descending');

            console.log('Final query object (refreshGrid):', this.query);
            console.log('=== END REFRESH GRID DEBUG ===');

            // Refresh the grid
            this.grid.query = this.query;
            this.grid.refresh();
        } catch (error) {
            console.error('Error refreshing grid:', error);
            this.hideLoading();
            this.showError('Failed to refresh grid: ' + error.message);
        }
    }

    // Refresh grid with time filters
    refreshByTime() {
        console.log('Refreshing grid with time filters');
        if (this.terminalId) {
            console.log('Including terminalId filter in time search:', this.terminalId);
        }
        
        if (!this.startTimePicker.value || !this.endTimePicker.value) {
            console.log('Time pickers not properly set');
            return;
        }

        this.showLoading();

        try {
            // Combine date and time for filtering
            const startDateTime = this.combineDateAndTime(this.dateStart, this.startTimePicker.value);
            const endDateTime = this.combineDateAndTime(this.dateEnd, this.endTimePicker.value);

            // Debug logging
            console.log('Combined date/time objects:', {
                startDateTime,
                endDateTime,
                startValid: startDateTime instanceof Date,
                endValid: endDateTime instanceof Date,
                terminalId: this.terminalId
            });

            // combineDateAndTime now returns null for invalid inputs, handle gracefully
            const safeStartDateTime = startDateTime || this.dateStart;
            const safeEndDateTime = endDateTime || this.dateEnd;

            // Build filter query with time using Syncfusion Predicate objects
            const startDate = this.formatDateForOData(safeStartDateTime);
            const endDate = this.formatDateForOData(safeEndDateTime);
            
            console.log('=== TIME FILTER DEBUG ===');
            console.log('Selected time range:', {
                startTime: this.startTimePicker.value ? `${this.startTimePicker.value.getHours()}:${this.startTimePicker.value.getMinutes()}` : 'null',
                endTime: this.endTimePicker.value ? `${this.endTimePicker.value.getHours()}:${this.endTimePicker.value.getMinutes()}` : 'null'
            });
            console.log('Combined date/time objects:', {
                safeStartDateTime,
                safeEndDateTime,
                startDateFormatted: startDate,
                endDateFormatted: endDate
            });

            // Create predicate-based time filter instead of raw string
            let timePredicate = new Predicate('DepositDateTime', 'greaterthanorequal', startDate)
                .and('DepositDateTime', 'lessthanorequal', endDate);
            
            // Add terminal ID filter if present
            if (this.terminalId) {
                timePredicate = timePredicate.and('TerminalId', 'equal', this.terminalId);
                console.log(`Added terminalId filter to time search: ${this.terminalId}`);
            }
            
            console.log('Predicate filter object:', timePredicate);
            console.log('=== END TIME FILTER DEBUG ===');

            // Create new query with time filter
            this.query = new Query()
                .where(timePredicate)
                .sortBy('SubmitSettlementDate', 'descending')
                .sortBy('DepositDateTime', 'descending');
            
            // Log the final query object for debugging
            console.log('Final query object:', this.query);

            // Refresh the grid
            this.grid.query = this.query;
            this.grid.refresh();
        } catch (error) {
            console.error('Error refreshing grid by time:', error);
            this.hideLoading();
            this.showError('Failed to refresh grid with time filters: ' + error.message);
        }
    }

    // Toolbar click event
    toolbarClick(args) {
        console.log('Toolbar clicked:', args.item.id);
        
        if (args.item.id === 'deposits-grid_excelexport') {
            this.exportToExcel();
        } else if (args.item.id === 'deposits-grid_pdfexport') {
            this.exportToPdf();
        }
    }

    // Export to Excel
    exportToExcel() {
        console.log('Exporting to Excel');
        
        const excelExportProperties = {
            fileName: `Deposits_${this.formatDateForFilename(new Date())}.xlsx`,
            includeGroupedData: true
        };
        
        this.grid.excelExport(excelExportProperties);
    }

    // Export to PDF
    exportToPdf() {
        console.log('Exporting to PDF');
        
        const pdfExportProperties = {
            fileName: `Deposits_${this.formatDateForFilename(new Date())}.pdf`,
            includeGroupedData: true
        };
        
        this.grid.pdfExport(pdfExportProperties);
    }

    // Grid failure event
    onGridFailure(args) {
        console.error('Grid failure:', args);
        this.hideLoading();
        
        // Handle specific empty data scenarios
        if (args.error && (
            args.error.includes('isCaptionRow') || 
            args.error.includes('Cannot read properties of undefined') ||
            args.error === 'The cellRenderer CaptionSummary is not found'
        )) {
            console.log('Handling empty data scenario - no deposits found for current filters');
            this.showNoDataMessage();
        } else {
            this.showError('Failed to load deposits data: ' + (args.error?.message || args.error || 'Unknown error'));
        }
    }

    // Before data bound event
    beforeDataBound() {
        console.log('Before data bound');
        this.showLoading();
    }

    // Data bound event
    dataBound() {
        // Check if we have data
        if (!this.grid.dataSource || this.grid.getCurrentViewRecords().length === 0) {
            console.log('No data bound to grid');
            this.hideLoading();
            return;
        }

        // Collapse all groups by default
        if (this.grid.groupModule) {
            this.grid.groupModule.collapseAll();
        }
        let captioncell = this.grid.element.getElementsByClassName('e-summarycell e-templatecell');
        let maxLoops = captioncell.length - 1;

        let captionElements = [];
        captionElements = this.grid.element.querySelectorAll('.e-groupcaption'); // group caption row element
        if (captionElements.length) {
            for (const element of captionElements) {
                element.title = "";
            }
        }

        // Helper function to get decimal value from grid column text content
        let getDecimalValueFromGridColumn = function (column) {
            try {
                // Get the text content from the cell
                let textContent = column.textContent || column.innerText || '';
                
                // Try to extract numeric value from text like "Total Deposited: 62,150"
                let match = textContent.match(/:\s*([\d,]+)/);
                if (match) {
                    let value = match[1].replace(/,/g, ""); // Remove all commas
                    return value;
                }
                
                // Fallback: try to find any number in the text
                let numberMatch = textContent.match(/([\d,]+)/);
                if (numberMatch) {
                    let value = numberMatch[1].replace(/,/g, "");
                    return value;
                }
                
                return null;
            } catch (error) {
                console.error('Error getting decimal value from grid column:', error, column);
                return null;
            }
        };

        // Filter caption cells to only include those with numeric content (actual aggregate values)
        let validCaptionCells = [];
        for (let i = 0; i < captioncell.length; i++) {
            let cell = captioncell[i];
            let textContent = cell.textContent || cell.innerText || '';
            
            // Look for cells that contain aggregate patterns
            if ((textContent.includes('Total') || textContent.includes('Capital Express Deduction:')) && textContent.includes(':')) {
                validCaptionCells.push(cell);
            }
        }

        // Process aggregate comparison based on mode
        if (this.isMca) {
            // MCA mode: validate Total Deposited = Capital Express Deduction + Net Settlement
            let totalDepositedCell = null;
            let capitalExpressCell = null;
            let netSettlementCell = null;
            
            for (let cell of validCaptionCells) {
                let textContent = cell.textContent || cell.innerText || '';
                if (textContent.includes('Total Deposited:')) {
                    totalDepositedCell = cell;
                } else if (textContent.includes('Capital Express Deduction:')) {
                    capitalExpressCell = cell;
                } else if (textContent.includes('Total Net Settlement:')) {
                    netSettlementCell = cell;
                }
            }
            
            if (totalDepositedCell && capitalExpressCell && netSettlementCell) {
                let totalDeposited = parseInt(getDecimalValueFromGridColumn(totalDepositedCell));
                let capitalExpress = parseInt(getDecimalValueFromGridColumn(capitalExpressCell));
                let netSettlement = parseInt(getDecimalValueFromGridColumn(netSettlementCell));
                
                if (!isNaN(totalDeposited) && !isNaN(capitalExpress) && !isNaN(netSettlement)) {
                    let expected = capitalExpress + netSettlement;
                    
                    if (totalDeposited === expected) {
                        // Valid: Total Deposited = Capital Express Deduction + Net Settlement
                        totalDepositedCell.classList.add('validcaptioncell');
                        capitalExpressCell.classList.add('validcaptioncell');
                        netSettlementCell.classList.add('validcaptioncell');
                    } else {
                        // Invalid: Show Total Deposited in red
                        totalDepositedCell.classList.add('invalidcaptioncell');
                        capitalExpressCell.classList.add('validcaptioncell');
                        netSettlementCell.classList.add('validcaptioncell');
                    }
                }
            }
        } else {
            // Non-MCA mode: original logic for pairs
            for (let j = 0; j < validCaptionCells.length; j += 2) {
                // Process pairs of valid caption cells
                if (j + 1 >= validCaptionCells.length) {
                    break; // No pair available
                }

                let gridcol1 = validCaptionCells[j];
                let gridcol2 = validCaptionCells[j + 1];

                let val1 = getDecimalValueFromGridColumn(gridcol1);
                let val2 = getDecimalValueFromGridColumn(gridcol2);

                // Skip if either value is null
                if (val1 === null || val2 === null) {
                    continue;
                }

                let column1 = parseInt(val1);
                let column2 = parseInt(val2);

                // Handle NaN values
                if (isNaN(column1) || isNaN(column2)) {
                    continue;
                }

                if (column1 !== column2) {
                    gridcol1.classList.add('invalidcaptioncell');
                    gridcol2.classList.add('invalidcaptioncell');
                } else {
                    gridcol1.classList.add('validcaptioncell');
                    gridcol2.classList.add('validcaptioncell');
                }
            }
        }

        // Add search clear functionality
        setTimeout(() => {
            let searchElement = this.grid.element.getElementsByClassName('e-search')[0];
            if (searchElement && !searchElement.classList.contains('clear')) {
                // Create clear button element
                let span = document.createElement('span');
                span.id = this.grid.element.id + '_searchcancelbutton';
                span.className = 'e-clear-icon';
                
                // Add click event listener
                span.addEventListener('click', (args) => {
                    let searchInput = document.querySelector('.e-search').getElementsByTagName('input')[0];
                    if (searchInput) {
                        searchInput.value = "";
                        this.grid.search("");
                    }
                });
                
                searchElement.appendChild(span);
                searchElement.classList.add('clear');
            }
        }, 1000);
        
        // Apply Capital Express Deduction styling for MCA mode
        if (this.isMca) {
            for (let i = 0; i < captioncell.length; i++) {
                let cell = captioncell[i];
                let textContent = cell.textContent || cell.innerText || '';
                
                if (textContent.includes('Capital Express Deduction:')) {
                    cell.classList.add('capital-express-deduction-caption');
                }
            }
        }
        
        console.log('Data bound');
        this.hideLoading();
    }

    // Grid load event
    load() {
        console.log('Grid loaded');
    }

    // Grid created event - called when grid is fully initialized
    onGridCreated() {
        console.log('Grid created and ready');
        // No longer calling refreshGrid() here to prevent second request
        // The grid was created with the proper query already configured
    }

    // Export group caption event
    exportGroupCaption(args) {
        // S.Danielz 2025-07-09
        // No need to reformat the data.
        // don't do anything
    }

    // Create depositor grid indicator (deposit type) - returns HTML string for Syncfusion Grid
    depositorGridIndicator(depositorCode) {
        // Safe null check for depositorCode
        const safeCode = depositorCode && typeof depositorCode === 'string' ? depositorCode : '';
        
        // Implement logic from Angular component:
        // If value is "00000000" -> manual deposit (ATM icon)
        // If value is not "00000000" -> drop deposit (User icon)
        const isManualDeposit = safeCode === "00000000";
        
        if (isManualDeposit) {
            // Manual deposit - ATM icon
            return `
                <div class="deposit-type-indicator manual-deposit" title="Manual Deposit (ATM)">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="#28a745" stroke-width="2">
                        <rect x="2" y="3" width="20" height="14" rx="2" ry="2"/>
                        <line x1="8" y1="21" x2="16" y2="21"/>
                        <line x1="12" y1="17" x2="12" y2="21"/>
                        <circle cx="12" cy="10" r="2"/>
                        <path d="M8 10h1m6 0h1"/>
                    </svg>
                </div>
            `;
        } else {
            // Drop deposit - User icon  
            return `
                <div class="deposit-type-indicator drop-deposit" title="Drop Deposit (User)">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="#007bff" stroke-width="2">
                        <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"/>
                        <circle cx="12" cy="7" r="4"/>
                    </svg>
                </div>
            `;
        }
    }

    // Create amount indicator - returns HTML string for Syncfusion Grid
    gridAmountIndicator(settledAmount, cashDeposited) {
        // Safe null check for amounts
        const safeSettledAmount = typeof settledAmount === 'number' ? settledAmount : 0;
        const safeCashDeposited = typeof cashDeposited === 'number' ? cashDeposited : 0;
        
        const isMatch = Math.abs(safeSettledAmount - safeCashDeposited) < 0.01;
        const matchClass = isMatch ? 'match' : 'mismatch';
        const symbol = isMatch ? '✓' : '✗';
        const title = isMatch ? 'Amounts match' : 'Amounts do not match';
        
        return `<div class="amount-indicator ${matchClass}" title="${title}">${symbol}</div>`;
    }

    // Template for MCA Capital Express Deduction column - shows blank for detail rows
    mcaDeductionTemplate(data) {
        // Always return empty string for detail rows - deductions only shown in caption aggregates
        return '';
    }

    // Template for MCA Net Settlement column - shows blank for detail rows
    mcaNetSettlementTemplate(data) {
        // Always return empty string for detail rows - net settlement only shown in caption aggregates
        return '';
    }

    // Custom aggregation for MCA Capital Express Deduction - gets value from first row in group
    customMcaDeductionSum(data, field) {
        if (!data || !Array.isArray(data) || data.length === 0) {
            return 0;
        }
        
        // For MCA mode, all rows in a group should have the same AdvanceDeductedAmount value
        // Just return the value from the first row
        let firstRow = data[0];
        if (firstRow && typeof firstRow[field] === 'number') {
            return firstRow[field];
        }
        
        return 0;
    }

    // Custom aggregate function for MCA validation
    customMcaValidation(data) {
        if (!data || !Array.isArray(data) || data.length === 0) {
            return false;
        }
        const totalDeposited = data.reduce((sum, item) => sum + (item.CashDeposited || 0), 0);
        const maxDeduction = Math.max(...data.map(item => item.AdvanceDeductedAmount || 0));
        const maxNet = Math.max(...data.map(item => item.AdvanceSettlementAmount || 0));
        return totalDeposited === maxDeduction + maxNet;
    }

    // Template for MCA indicator in group caption
    mcaIndicatorTemplate(args) {
        const isMatch = args.custom;
        const matchClass = isMatch ? 'match' : 'mismatch';
        const symbol = isMatch ? '✓' : '✗';
        const title = isMatch ? 'Amounts match' : 'Amounts do not match';
        return `<div class="amount-indicator ${matchClass}" title="${title}">${symbol}</div>`;
    }

    // Show loading indicator
    showLoading() {
        const loading = document.getElementById('loading-indicator');
        if (loading) {
            loading.style.display = 'block';
        }
    }

    // Hide loading indicator
    hideLoading() {
        const loading = document.getElementById('loading-indicator');
        if (loading) {
            loading.style.display = 'none';
        }
    }

    // Show error message
    showError(message) {
        console.error('Deposits error:', message);
        
        const errorDiv = document.getElementById('error-message');
        const errorDetails = document.getElementById('error-details');
        
        if (errorDiv && errorDetails) {
            errorDetails.textContent = message;
            errorDiv.style.display = 'block';
        } else {
            alert('Error: ' + message);
        }
    }

    // Show no data message
    showNoDataMessage() {
        console.log('No deposits found for current filters');
        
        // Hide error message if showing
        this.hideError();
        
        // Show a user-friendly no data message
        const gridContainer = document.getElementById('deposits-grid');
        if (gridContainer) {
            gridContainer.innerHTML = `
                <div class="no-data-message" style="
                    text-align: center;
                    padding: 40px 20px;
                    color: #666;
                    font-size: 16px;
                    background: #f8f9fa;
                    border: 1px solid #dee2e6;
                    border-radius: 4px;
                ">
                    <div style="font-size: 48px; margin-bottom: 16px;">📊</div>
                    <h3 style="margin: 0 0 8px 0; color: #495057;">No Deposits Found</h3>
                    <p style="margin: 0; font-size: 14px;">
                        No deposits match your current filter criteria. 
                        ${this.terminalId ? `Terminal ID: ${this.terminalId}` : ''}
                    </p>
                    <p style="margin: 8px 0 0 0; font-size: 12px; color: #868e96;">
                        Try adjusting your date range or search filters.
                    </p>
                </div>
            `;
        }
    }

    // Hide error message
    hideError() {
        const errorDiv = document.getElementById('error-message');
        if (errorDiv) {
            errorDiv.style.display = 'none';
        }
    }

    // Utility methods
    formatDateForOData(date) {
        try {
            if (!date) {
                console.error('Null or undefined date passed to formatDateForOData');
                // Return a default date string instead of null to prevent "null" in query
                return this.formatDateTimeAsLocal(new Date());
            }
            
            if (!(date instanceof Date)) {
                console.error('Non-Date object passed to formatDateForOData:', typeof date, date);
                // Try to convert to Date if possible, otherwise return current date
                try {
                    const convertedDate = new Date(date);
                    if (!isNaN(convertedDate.getTime())) {
                        return this.formatDateTimeAsLocal(convertedDate);
                    }
                } catch (conversionError) {
                    console.error('Failed to convert to Date:', conversionError);
                }
                return this.formatDateTimeAsLocal(new Date());
            }
            
            if (isNaN(date.getTime())) {
                console.error('Invalid Date object passed to formatDateForOData:', date);
                // Return current date instead of null
                return this.formatDateTimeAsLocal(new Date());
            }
            
            const formattedString = this.formatDateTimeAsLocal(date);
            console.log('Formatted date for OData (local time):', date, '->', formattedString);
            return formattedString;
        } catch (error) {
            console.error('Error in formatDateForOData:', error, 'Input:', date);
            // Return current date as fallback instead of null
            return this.formatDateTimeAsLocal(new Date());
        }
    }

    // Format date preserving local time values exactly as entered
    formatDateTimeAsLocal(date) {
        if (!date || !(date instanceof Date) || isNaN(date.getTime())) {
            return new Date().toISOString();
        }

        // Extract local time components directly and format as ISO string
        // This preserves the exact time values the user selected
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        const hours = String(date.getHours()).padStart(2, '0');
        const minutes = String(date.getMinutes()).padStart(2, '0');
        const seconds = String(date.getSeconds()).padStart(2, '0');
        const milliseconds = String(date.getMilliseconds()).padStart(2, '0'); // Use 2 digits like Angular

        // Format with Z suffix so CustomV4Adaptor can properly clean it up
        const localISOString = `${year}-${month}-${day}T${hours}:${minutes}:${seconds}.${milliseconds}Z`;
        
        console.log('Local time formatting (exact preservation):', {
            originalDate: date,
            userSelectedTime: `${hours}:${minutes}:${seconds}`,
            formattedForBackend: localISOString,
            note: 'Time preserved exactly as user selected, with Z suffix for CustomV4Adaptor'
        });
        
        return localISOString;
    }

    formatDateForFilename(date) {
        return date.toISOString().split('T')[0];
    }

    formatDate(date) {
        return new Date(date).toLocaleDateString('en-US', {
            day: '2-digit',
            month: 'short',
            year: 'numeric'
        });
    }

    combineDateAndTime(date, time) {
        try {
            if (!date || !(date instanceof Date) || isNaN(date.getTime())) {
                console.error('Invalid date passed to combineDateAndTime:', date);
                return null;
            }
            
            if (!time || !(time instanceof Date) || isNaN(time.getTime())) {
                console.error('Invalid time passed to combineDateAndTime:', time);
                return null;
            }
            
            const combined = new Date(date);
            combined.setHours(time.getHours(), time.getMinutes(), time.getSeconds(), 0);
            
            if (isNaN(combined.getTime())) {
                console.error('Failed to create valid combined date/time');
                return null;
            }
            
            return combined;
        } catch (error) {
            console.error('Error in combineDateAndTime:', error, 'date:', date, 'time:', time);
            return null;
        }
    }

    getWeekStart() {
        const date = new Date();
        const day = date.getDay();
        const diff = date.getDate() - day + (day === 0 ? -6 : 1);
        return new Date(date.setDate(diff));
    }

    getMonthStart() {
        const date = new Date();
        return new Date(date.getFullYear(), date.getMonth(), 1);
    }

    getLastMonthStart() {
        const date = new Date();
        return new Date(date.getFullYear(), date.getMonth() - 1, 1);
    }

    getLastMonthEnd() {
        const date = new Date();
        return new Date(date.getFullYear(), date.getMonth(), 0);
    }

    // Set a date to the start of the day (00:00:00.000)
    setToStartOfDay(date) {
        if (!date || !(date instanceof Date) || isNaN(date.getTime())) {
            console.error('Invalid date passed to setToStartOfDay:', date);
            return new Date();
        }
        
        const startOfDay = new Date(date);
        startOfDay.setHours(0, 0, 0, 0);
        return startOfDay;
    }

    // Set a date to the end of the day (23:59:59.999)
    setToEndOfDay(date) {
        if (!date || !(date instanceof Date) || isNaN(date.getTime())) {
            console.error('Invalid date passed to setToEndOfDay:', date);
            return new Date();
        }
        
        const endOfDay = new Date(date);
        endOfDay.setHours(23, 59, 59, 999);
        return endOfDay;
    }

    // Cleanup when leaving the page
    destroy() {
        console.log('Destroying deposits page');
        
        if (this.grid) {
            this.grid.destroy();
        }
        if (this.dateRangePicker) {
            this.dateRangePicker.destroy();
        }
        if (this.startTimePicker) {
            this.startTimePicker.destroy();
        }
        if (this.endTimePicker) {
            this.endTimePicker.destroy();
        }
        
        this.initialized = false;
    }
}

// Create global instance
const depositsModule = new DepositsModule();

// Export for use in other modules
export default depositsModule;

// Make available globally
if (typeof window !== 'undefined') {
    window.depositsModule = depositsModule;
    document.addEventListener('DOMContentLoaded', () => {
        window.depositsModule.init();
    });
} 