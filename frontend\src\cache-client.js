// # cache-client.js
//
// Cache Management Client - Frontend Cache Busting System
//
// This module provides comprehensive client-side cache management and automatic
// update detection for the MyATM Embedded UI application. It works in conjunction
// with the backend cache-busting system to ensure users always receive the latest
// application versions.
//
// ## Key Features
// - **Automatic Version Detection**: Periodically checks for new application versions
// - **Asset Management**: Handles cache-busted URLs for scripts and stylesheets
// - **Update Notifications**: Provides user-friendly update notifications
// - **Development Support**: Includes cache refresh utilities for development
// - **Event System**: Dispatches custom events for version changes
//
// ## Architecture
// The client communicates with backend endpoints to:
// - GET /api/cache/version - Retrieve current version and asset manifest
// - GET /api/cache/manifest - Get detailed asset mapping
// - GET /api/cache/refresh - Trigger cache refresh (development only)
//
// ## Usage
// ```javascript
// const client = new CacheBustingClient();
// await client.init({
//     checkInterval: 30000,  // Check every 30 seconds
//     autoRefresh: true      // Auto-refresh on version change
// });
// ```
//
// ## Integration
// This module is automatically imported by main.js and provides cache-busting
// functionality for the entire application. It works seamlessly with the Rust
// backend's cache management system.

/**
 * Client-side cache busting utilities for Axum backend
 */

class CacheBustingClient {
    constructor() {
        this.currentVersion = null;
        this.assetManifest = null;
        this.versionCheckInterval = null;
        this.baseUrl = window.location.origin;
    }

    /**
     * Initialize cache busting client
     * @param {Object} options - Configuration options
     * @param {number} options.checkInterval - Interval in milliseconds to check for updates
     * @param {boolean} options.autoRefresh - Whether to auto-refresh on version change
     */
    async init(options = {}) {
        const {
            checkInterval = 30000, // 30 seconds
            autoRefresh = true
        } = options;

        try {
            // Get initial version info
            await this.fetchVersionInfo();
            
            // Set up periodic version checking
            if (checkInterval > 0) {
                this.versionCheckInterval = setInterval(async () => {
                    await this.checkForUpdates(autoRefresh);
                }, checkInterval);
            }
            
            console.log('🔧 Cache busting client initialized', {
                version: this.currentVersion,
                checkInterval,
                autoRefresh
            });
        } catch (error) {
            console.error('❌ Failed to initialize cache busting client:', error);
        }
    }

    /**
     * Fetch current version info from server
     */
    async fetchVersionInfo() {
        try {
            const response = await fetch(`${this.baseUrl}/api/cache/version`);
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}`);
            }
            
            const data = await response.json();
            this.currentVersion = data.version;
            this.assetManifest = data.assets;
            
            return data;
        } catch (error) {
            console.error('❌ Failed to fetch version info:', error);
            throw error;
        }
    }

    /**
     * Fetch asset manifest from server
     */
    async fetchAssetManifest() {
        try {
            const response = await fetch(`${this.baseUrl}/api/cache/manifest`);
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}`);
            }
            
            const data = await response.json();
            this.assetManifest = data.assets;
            
            return data;
        } catch (error) {
            console.error('❌ Failed to fetch asset manifest:', error);
            throw error;
        }
    }

    /**
     * Check for version updates
     * @param {boolean} autoRefresh - Whether to auto-refresh on version change
     */
    async checkForUpdates(autoRefresh = false) {
        try {
            const currentVersion = this.currentVersion;
            await this.fetchVersionInfo();
            
            if (currentVersion && currentVersion !== this.currentVersion) {
                console.log('🔄 New version detected:', {
                    old: currentVersion,
                    new: this.currentVersion
                });
                
                // Trigger custom event
                window.dispatchEvent(new CustomEvent('cacheVersionUpdated', {
                    detail: {
                        oldVersion: currentVersion,
                        newVersion: this.currentVersion,
                        assetManifest: this.assetManifest
                    }
                }));
                
                if (autoRefresh) {
                    this.refreshPage();
                }
            }
        } catch (error) {
            console.error('❌ Failed to check for updates:', error);
        }
    }

    /**
     * Refresh cache on server (development only)
     */
    async refreshCache() {
        try {
            const response = await fetch(`${this.baseUrl}/api/cache/refresh`);
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}`);
            }
            
            const data = await response.json();
            console.log('🔄 Cache refreshed:', data);
            
            // Update local version info
            await this.fetchVersionInfo();
            
            return data;
        } catch (error) {
            console.error('❌ Failed to refresh cache:', error);
            throw error;
        }
    }

    /**
     * Get asset URL with cache busting
     * @param {string} assetName - Name of the asset
     * @returns {string} - Asset URL with cache busting
     */
    getAssetUrl(assetName) {
        if (this.assetManifest && this.assetManifest[assetName]) {
            return this.assetManifest[assetName].url || `${assetName}?v=${this.currentVersion}`;
        }
        return this.currentVersion ? `${assetName}?v=${this.currentVersion}` : assetName;
    }

    /**
     * Load script with cache busting
     * @param {string} src - Script source URL
     * @param {Object} options - Loading options
     * @returns {Promise} - Promise that resolves when script is loaded
     */
    async loadScript(src, options = {}) {
        const {
            id = null,
            async = true,
            defer = false,
            type = 'text/javascript'
        } = options;

        return new Promise((resolve, reject) => {
            // Remove existing script if it exists
            if (id) {
                const existingScript = document.getElementById(id);
                if (existingScript) {
                    existingScript.remove();
                }
            }

            const script = document.createElement('script');
            script.src = this.getAssetUrl(src);
            script.async = async;
            script.defer = defer;
            script.type = type;
            
            if (id) {
                script.id = id;
            }

            script.onload = () => {
                console.log('✅ Script loaded:', script.src);
                resolve();
            };
            
            script.onerror = (error) => {
                console.error('❌ Script failed to load:', script.src, error);
                reject(error);
            };

            document.head.appendChild(script);
        });
    }

    /**
     * Load CSS with cache busting
     * @param {string} href - CSS file URL
     * @param {Object} options - Loading options
     * @returns {Promise} - Promise that resolves when CSS is loaded
     */
    async loadCSS(href, options = {}) {
        const {
            id = null,
            media = 'all'
        } = options;

        return new Promise((resolve, reject) => {
            // Remove existing link if it exists
            if (id) {
                const existingLink = document.getElementById(id);
                if (existingLink) {
                    existingLink.remove();
                }
            }

            const link = document.createElement('link');
            link.rel = 'stylesheet';
            link.href = this.getAssetUrl(href);
            link.media = media;
            
            if (id) {
                link.id = id;
            }

            link.onload = () => {
                console.log('✅ CSS loaded:', link.href);
                resolve();
            };
            
            link.onerror = (error) => {
                console.error('❌ CSS failed to load:', link.href, error);
                reject(error);
            };

            document.head.appendChild(link);
        });
    }

    /**
     * Refresh the current page
     */
    refreshPage() {
        console.log('🔄 Refreshing page for cache update...');
        window.location.reload();
    }

    /**
     * Get current cache version
     * @returns {string} - Current cache version
     */
    getCurrentVersion() {
        return this.currentVersion;
    }

    /**
     * Get asset manifest
     * @returns {Object} - Asset manifest
     */
    getAssetManifest() {
        return this.assetManifest;
    }

    /**
     * Show update notification
     * @param {Object} options - Notification options
     */
    showUpdateNotification(options = {}) {
        const {
            message = 'A new version is available. Refresh to update?',
            autoHide = false,
            hideDelay = 5000
        } = options;

        // Create notification element
        const notification = document.createElement('div');
        notification.id = 'cache-update-notification';
        notification.innerHTML = `
            <div style="
                position: fixed;
                top: 20px;
                right: 20px;
                background: #4CAF50;
                color: white;
                padding: 15px 20px;
                border-radius: 5px;
                box-shadow: 0 2px 10px rgba(0,0,0,0.2);
                z-index: 10000;
                font-family: sans-serif;
                font-size: 14px;
                max-width: 300px;
            ">
                <div style="margin-bottom: 10px;">${message}</div>
                <button id="refresh-btn" style="
                    background: white;
                    color: #4CAF50;
                    border: none;
                    padding: 5px 10px;
                    border-radius: 3px;
                    cursor: pointer;
                    margin-right: 10px;
                ">Refresh</button>
                <button id="dismiss-btn" style="
                    background: transparent;
                    color: white;
                    border: 1px solid white;
                    padding: 5px 10px;
                    border-radius: 3px;
                    cursor: pointer;
                ">Dismiss</button>
            </div>
        `;

        document.body.appendChild(notification);

        // Add event listeners
        document.getElementById('refresh-btn').addEventListener('click', () => {
            this.refreshPage();
        });

        document.getElementById('dismiss-btn').addEventListener('click', () => {
            notification.remove();
        });

        // Auto-hide if requested
        if (autoHide) {
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.remove();
                }
            }, hideDelay);
        }
    }

    /**
     * Stop version checking
     */
    stop() {
        if (this.versionCheckInterval) {
            clearInterval(this.versionCheckInterval);
            this.versionCheckInterval = null;
        }
    }
}

// Global instance
window.CacheBustingClient = CacheBustingClient;

// Auto-initialize if enabled
if (window.AUTO_INIT_CACHE_BUSTING !== false) {
    document.addEventListener('DOMContentLoaded', () => {
        const client = new CacheBustingClient();
        client.init({
            checkInterval: 30000, // 30 seconds
            autoRefresh: false // Don't auto-refresh, show notification instead
        });

        // Listen for version updates (silent logging only)
        window.addEventListener('cacheVersionUpdated', (event) => {
            console.log('Cache version updated silently:', event.detail);
            // Silent operation - no automatic notifications
        });

        // Make client globally available
        window.cacheBustingClient = client;
    });
} 