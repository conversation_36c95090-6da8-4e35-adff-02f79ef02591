# Modular Architecture Guide

This document describes the new modular architecture of the MyATM Embedded UI application and how to extend it with new pages.

## Overview

The application has been restructured to be highly modular and extensible. The architecture follows these principles:

- **Feature-based organization**: Each major feature (like deposits) has its own subfolder
- **Modular routing**: Pages are registered in a central registry for easy extensibility
- **Separation of concerns**: Frontend and backend code are organized by feature
- **Environment-specific configuration**: Configuration variables are scoped to specific features

## Project Structure

```
myatm-embedded-ui/
├── frontend/src/
│   ├── deposits/                 # Deposits feature module
│   │   ├── deposits.js          # Deposits page logic
│   │   ├── deposits.html        # Deposits page template
│   │   └── deposits.css         # Deposits page styles
│   ├── app.js                   # Main application and routing
│   ├── router.js                # SPA router
│   └── main.js                  # Application entry point
├── backend/src/
│   ├── deposits/                # Deposits backend module
│   │   ├── mod.rs              # Module definition
│   │   ├── config.rs           # Deposits configuration
│   │   ├── handlers.rs         # Deposits API handlers
│   │   └── odata.rs            # OData parsing logic
│   ├── config.rs               # Main configuration
│   └── main.rs                 # Application entry point
└── env.example                 # Environment configuration
```

## Configuration Structure

### Environment Variables

Configuration is now feature-specific:

```bash
# Deposits API Configuration
DEPOSITS_EXTERNAL_API_URL=https://merchant-deposits-myatm-api.paycorp.co.za
DEPOSITS_EXTERNAL_API_TIMEOUT=30
```

### Backend Configuration

The backend configuration is modular:

```rust
// config.rs
pub struct AppConfig {
    pub server: ServerConfig,
    pub database: DatabaseConfig,
    pub deposits: DepositsConfig,  // Feature-specific config
    pub auth: AuthConfig,
}

// deposits/config.rs
pub struct DepositsConfig {
    pub external_api: DepositsExternalApiConfig,
}
```

## Page Registry System

The frontend uses a page registry system for easy extensibility:

```javascript
// Pages are registered in app.js
pageRegistry.set('deposits', {
    title: 'Deposits',
    path: '/deposits',
    icon: '💰',
    description: 'View and manage deposit records',
    handler: () => showDepositsPage(depositsModule),
    module: depositsModule
});
```

## Adding a New Page

To add a new page (e.g., "Transactions"), follow these steps:

### 1. Create Frontend Module

```bash
mkdir frontend/src/transactions
```

Create the following files:

**frontend/src/transactions/transactions.js**
```javascript
// Transactions page module
class TransactionsModule {
    constructor() {
        this.initialized = false;
    }

    async init() {
        if (this.initialized) return;
        
        await this.loadTemplate();
        this.initializeComponents();
        this.initialized = true;
    }

    async loadTemplate() {
        const response = await fetch('./src/transactions/transactions.html');
        const html = await response.text();
        document.getElementById('main-content').innerHTML = html;
    }

    initializeComponents() {
        // Initialize your page components here
    }

    destroy() {
        // Cleanup logic
        this.initialized = false;
    }
}

const transactionsModule = new TransactionsModule();
export default transactionsModule;
```

**frontend/src/transactions/transactions.html**
```html
<link rel="stylesheet" href="./transactions.css">

<div id="transactions-page" class="page-container">
    <div class="page-header">
        <h1>Transactions</h1>
        <p>View and manage transaction records</p>
    </div>
    
    <div class="page-content">
        <!-- Your page content here -->
    </div>
</div>
```

**frontend/src/transactions/transactions.css**
```css
/* Transactions page styles */
.page-container {
    padding: 20px;
    max-width: 1200px;
    margin: 0 auto;
}

.page-header {
    margin-bottom: 30px;
    text-align: center;
}

.page-content {
    background: white;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}
```

### 2. Create Backend Module

```bash
mkdir backend/src/transactions
```

Create the following files:

**backend/src/transactions/mod.rs**
```rust
pub mod config;
pub mod handlers;

pub use config::TransactionsConfig;
pub use handlers::*;
```

**backend/src/transactions/config.rs**
```rust
use serde::{Deserialize, Serialize};
use std::env;

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TransactionsConfig {
    pub external_api: TransactionsExternalApiConfig,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TransactionsExternalApiConfig {
    pub base_url: String,
    pub timeout_seconds: u64,
}

impl TransactionsConfig {
    pub fn from_env() -> Result<Self, Box<dyn std::error::Error>> {
        Ok(TransactionsConfig {
            external_api: TransactionsExternalApiConfig {
                base_url: env::var("TRANSACTIONS_EXTERNAL_API_URL")
                    .unwrap_or_else(|_| "https://transactions-api.example.com".to_string()),
                timeout_seconds: env::var("TRANSACTIONS_EXTERNAL_API_TIMEOUT")
                    .unwrap_or_else(|_| "30".to_string())
                    .parse()
                    .unwrap_or(30),
            },
        })
    }
}
```

**backend/src/transactions/handlers.rs**
```rust
use axum::{extract::State, response::Json, http::StatusCode};
use serde_json::Value;
use tracing::info;
use crate::config::AppConfig;

pub async fn transactions_proxy(
    State(state): State<AppConfig>,
) -> Result<Json<Value>, StatusCode> {
    info!("Handling transactions request");
    
    // Your handler logic here
    let response = serde_json::json!({
        "message": "Transactions endpoint",
        "config": state.transactions.external_api.base_url
    });
    
    Ok(Json(response))
}
```

### 3. Update Configuration

**Add to env.example:**
```bash
# Transactions API Configuration
TRANSACTIONS_EXTERNAL_API_URL=https://transactions-api.example.com
TRANSACTIONS_EXTERNAL_API_TIMEOUT=30
```

**Update backend/src/config.rs:**
```rust
use crate::transactions::TransactionsConfig;

pub struct AppConfig {
    pub server: ServerConfig,
    pub database: DatabaseConfig,
    pub deposits: DepositsConfig,
    pub transactions: TransactionsConfig,  // Add this
    pub auth: AuthConfig,
}

impl AppConfig {
    pub fn from_env() -> Result<Self, Box<dyn std::error::Error>> {
        Ok(AppConfig {
            // ... existing fields
            transactions: TransactionsConfig::from_env()?,
            // ... rest of fields
        })
    }
}
```

### 4. Register Routes

**Update backend/src/main.rs:**
```rust
mod transactions;  // Add this

// In the router setup:
.route("/api/transactions", get(transactions::handlers::transactions_proxy))
```

### 5. Register Frontend Page

**Update frontend/src/app.js:**
```javascript
function registerPages(depositsModule) {
    // ... existing pages
    
    // Import and register transactions module
    import('./transactions/transactions.js').then(({ default: transactionsModule }) => {
        pageRegistry.set('transactions', {
            title: 'Transactions',
            path: '/transactions',
            icon: '💳',
            description: 'View and manage transaction records',
            handler: () => showTransactionsPage(transactionsModule),
            module: transactionsModule
        });
    });
}

function showTransactionsPage(transactionsModule) {
    console.log('Showing transactions page');
    
    if (transactionsModule.initialized) {
        transactionsModule.destroy();
    }
    
    transactionsModule.init();
}
```

## Best Practices

1. **Feature Isolation**: Keep all feature-related code in its own subfolder
2. **Environment Configuration**: Use feature-specific environment variables
3. **Modular Imports**: Import modules dynamically to avoid loading unnecessary code
4. **Consistent Naming**: Use consistent naming patterns across features
5. **Error Handling**: Implement proper error handling in both frontend and backend
6. **Documentation**: Document your new features and their configuration

## Testing

After adding a new page, test the following:

1. **Navigation**: Ensure the page appears in the home page navigation
2. **Routing**: Verify the route works correctly
3. **API Integration**: Test any backend API endpoints
4. **Responsive Design**: Ensure the page works on different screen sizes
5. **Error Handling**: Test error scenarios and fallbacks

This modular architecture makes it easy to add new features while maintaining clean separation of concerns and avoiding conflicts between different parts of the application. 