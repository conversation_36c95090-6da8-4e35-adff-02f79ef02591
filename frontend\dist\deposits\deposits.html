<!-- # deposits.html -->
<!--
Deposits Page Template - SPA Component

This HTML template provides the structure for the deposits management page,
featuring a Syncfusion Grid with filtering capabilities. It's loaded dynamically
by the SPA router when users navigate to /deposits.

## Template Structure
- **Filters Section**: Date range picker and time pickers for data filtering
- **Grid Container**: Houses the Syncfusion DataGrid for deposits display
- **Loading Indicator**: Visual feedback during data loading operations
- **Error Message**: User-friendly error display with retry functionality

## Key Features
- Responsive design with flexible grid layout
- Date/time filtering with Syncfusion controls
- Loading states and error handling UI
- Integrated with deposits.css for styling
- Dynamic initialization via deposits.js module

## Dependencies
- Syncfusion EJ2 components (loaded globally)
- deposits.css for component styling
- deposits.js for JavaScript functionality

## Integration
This template integrates with:
- deposits.js: Handles all JavaScript functionality and grid initialization
- deposits.css: Provides styling for layout and components
- Backend API: /api/deposits endpoint for data retrieval
-->

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8" />
    <title>myDeposits</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta name="description" content="myDeposits - Manage your deposits efficiently with a user-friendly data grid interface." />
    <!-- CSP is now set via HTTP headers from the backend -->
    <link rel="icon" type="image/svg+xml" href="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32'%3E%3Crect x='2' y='2' width='10' height='10' fill='%23007bff'/%3E%3Crect x='12' y='2' width='10' height='10' fill='%23007bff'/%3E%3Crect x='22' y='2' width='8' height='10' fill='%23007bff'/%3E%3Crect x='2' y='12' width='10' height='10' fill='%23007bff'/%3E%3Crect x='12' y='12' width='10' height='10' fill='%23007bff'/%3E%3Crect x='22' y='12' width='8' height='10' fill='%23007bff'/%3E%3Crect x='2' y='22' width='10' height='8' fill='%23007bff'/%3E%3Crect x='12' y='22' width='10' height='8' fill='%23007bff'/%3E%3Crect x='22' y='22' width='8' height='8' fill='%23007bff'/%3E%3C/svg%3E" />
    <link href="/static/themes/material/material.min.css" rel="stylesheet" />
    
    
    
    <!-- Bundled CSS will be injected here by the build process -->
    <link rel="stylesheet" href="main.css?v=20250711210013">
    <link rel="stylesheet" href="/static/deposits/deposits-AE3O6HO6.css?v=20250716144932">
</head>
<body>

<div id="main-content">
  <div id="deposits-page" class="page-container">
      <div id="filters" class="filters-container">
          <div class="filter-group">
              <div id="datePicker" class="filter-item">
                  <input type="text" id="drpDate" />
              </div>
              <div id="timePickers" class="filter-item">
                  <div class="time-inputs">
                      <label for="startPicker">Start Time:</label>
                      <input type="text" id="startPicker" />
                      <label for="endPicker">End Time:</label>
                      <input type="text" id="endPicker" />
                  </div>
              </div>
          </div>
      </div>

      <div id="grid-container" class="grid-container">
          <div id="deposits-grid"></div>
      </div>

      <div id="loading-indicator" class="loading-indicator" style="display: none;">
          <div class="spinner"></div>
          <p>Loading deposits...</p>
      </div>

      <div id="error-message" class="error-message" style="display: none;">
          <div class="error-content">
              <h3>Error Loading Deposits</h3>
              <p id="error-details"></p>
              <button onclick="depositsModule.refreshGrid()">Retry</button>
          </div>
      </div>
  </div>
</div>

    <script src="/static/deposits/deposits-BW63WFBR.js?v=20250716144932"></script>
</body>
</html>