### ✅ Implementation Plan: MCA (Merchant Cash-Advance) Mode

**Overview**

We add a third display mode to the Deposits page, triggered by the query-string flag `mca=true`.

* When present, the BFF proxies to `/odata/externalDsMcaMerchantDeposits`.
* The grid shows the MCA column set
  `Deposit Date | Terminal ID | Depositor Name | Trace No | Deposited | Capital Express Deduction | Net Settlement`.
* If both `multiMerchant=true` and `mca=true` are supplied, multi-merchant mode wins. Otherwise the default single-merchant view applies.

## 1 Prepare workspace

Pull code, run `npm install`, `cargo build`, then execute the full test suite.

## 2 Front-end: read the new flag

In `frontend/src/deposits/deposits.js`:

```js
this.isMca = router.getQueryParam?.('mca') === 'true';

const urlParams = new URLSearchParams(window.location.search);
this.isMca = urlParams.get('mca') === 'true';
```

## 3 Front-end: build data-source URL

```js
const parts = [];
if (this.terminalId)    parts.push(`terminalId=${encodeURIComponent(this.terminalId)}`);
if (this.multiMerchant) parts.push('multiMerchant=true');
if (this.isMca)         parts.push('mca=true');

const qs = parts.length ? '?' + parts.join('&') : '';
const dataSourceUrl = '/api/deposits' + qs;
```

## 4 Front-end: columns for MCA mode

Add a branch in `getGridColumns()`:

```js
if (this.isMca) {
    return [
        { field:'DepositDateTime',  headerText:'Deposit Date',  textAlign:'Center', format:this.dateFormatTimeOptions, width:180 },
        { field:'TerminalId',      headerText:'Terminal ID',   textAlign:'Center',  width:120 },
        { field:'DepositorName',   headerText:'Depositor Name',textAlign:'Left',    width:200 },
        { field:'TraceNr',         headerText:'Trace No',      textAlign:'Center',  width:120 },
        { field:'CashDeposited',   headerText:'Deposited',     textAlign:'Right',   width:120 },
        { field:'AdvanceDeductedAmount', headerText:'Capital Express Deduction', textAlign:'Right', width:160 },
        { field:'SettledAmount',   headerText:'Net Settlement',textAlign:'Right',   width:120 }
    ];
}
```

If Net Settlement must be calculated as `CashDeposited – AdvanceDeductedAmount`, let me know.

## 5 Back-end: select the correct endpoint

In `backend/src/deposits/handlers.rs`:

```rust
let multi = params.get("multiMerchant").map(|v| v == "true").unwrap_or(false);
let mca   = params.get("mca").map(|v| v == "true").unwrap_or(false);

let endpoint = if multi {
    "externalDsMultiMerchantDeposits"
} else if mca {
    "externalDsMcaMerchantDeposits"
} else {
    "externalDsMerchantDepositAggregates"
};

let external_url = format!("{}/odata/{}", base_url, endpoint);
```

## 6 Back-end: extend model

Add the MCA-specific fields if missing:

```rust
#[serde(rename = "AdvanceSettlementAmount", serialize_with = "serialize_f64_as_whole")]
pub advance_settlement_amount: f64,
#[serde(rename = "AdvanceDeductedAmount",   serialize_with = "serialize_f64_as_whole")]
pub advance_deducted_amount: f64,
```

## 7 Tests

Back-end tests

* `?mca=true` sends the MCA endpoint.
* `multiMerchant=true&mca=true` still uses the multi-merchant endpoint.

Front-end tests

* `/deposits?mca=true` shows the seven MCA columns.
* Default view hides them.

## 8 Aggregates (optional)

Add totals for Capital Express Deduction and Net Settlement if required.

## 9 Documentation

Update `README.md`:

* New flag `mca`
* Precedence rules
* Example URL `/deposits?terminalId=CXBH0006&mca=true`

## 10 Commit and PR

```
feat(deposits): add MCA mode (mca=true) with endpoint switch and MCA column set
```

Include explanation of flags, endpoint logic, columns, and tests.

