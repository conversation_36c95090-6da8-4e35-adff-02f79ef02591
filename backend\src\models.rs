// # models.rs
//
// Data models and structures for the merchant deposits API.

use chrono::{NaiveDateTime, DateTime, Utc, FixedOffset};
use chrono::TimeZone;
use rust_decimal::Decimal;
use rust_decimal::prelude::ToPrimitive;
use serde::{Deserialize, Serialize, Serializer};
use uuid::Uuid;

fn serialize_decimal_as_whole<S>(value: &Decimal, serializer: S) -> Result<S::Ok, S::Error>
where
    S: Serializer,
{
    serializer.serialize_i64(value.round().to_i64().unwrap_or(0))
}

fn serialize_f64_as_whole<S>(value: &f64, serializer: S) -> Result<S::Ok, S::Error>
where
    S: Serializer,
{
    serializer.serialize_i64(value.round() as i64)
}

// Custom serializer for SAST (+02:00) from UTC
pub fn serialize_utc_as_sast<S>(dt: &DateTime<Utc>, serializer: S) -> Result<S::Ok, S::Error>
where
    S: serde::Serializer,
{
    let offset = FixedOffset::east_opt(2 * 3600).unwrap();
    let dt_with_offset = dt.with_timezone(&offset);
    serializer.serialize_str(&dt_with_offset.to_rfc3339())
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MerchantDeposit {
    #[serde(rename = "RecordId")]
    pub record_id: i64,
    #[serde(rename = "RecordGuid")]
    pub record_guid: Option<Uuid>,
    #[serde(rename = "SubmitSettlementDate")]
    pub submit_settlement_date: NaiveDateTime,
    #[serde(rename = "DepositDateTime")]
    pub deposit_date_time: NaiveDateTime,
    #[serde(rename = "TerminalId")]
    pub terminal_id: String,
    #[serde(rename = "DepositorName")]
    pub depositor_name: Option<String>,
    #[serde(rename = "DepositorCode")]
    pub depositor_code: Option<String>,
    #[serde(rename = "CashDeposited", serialize_with = "serialize_decimal_as_whole")]
    pub cash_deposited: Decimal,
    #[serde(rename = "SettledAmount", serialize_with = "serialize_decimal_as_whole")]
    pub settled_amount: Decimal,
    #[serde(rename = "TotalUnsettled", serialize_with = "serialize_decimal_as_whole")]
    pub total_unsettled: Decimal,
    #[serde(rename = "TraceNr")]
    pub trace_nr: Option<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ODataResponse<T> {
    #[serde(rename = "@odata.context")]
    pub odata_context: String,
    #[serde(rename = "@odata.count")]
    pub odata_count: i64,
    pub value: Vec<T>,
}



#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ExternalDsMcaMerchantDeposit {
    #[serde(rename = "RecordId")]
    pub record_id: i64,
    #[serde(rename = "RecordGuid")]
    pub record_guid: Option<Uuid>,
    #[serde(rename = "SubmitSettlementDate", serialize_with = "serialize_utc_as_sast")]
    pub submit_settlement_date: DateTime<Utc>,
    #[serde(rename = "DepositDateTime", serialize_with = "serialize_utc_as_sast")]
    pub deposit_date_time: DateTime<Utc>,
    #[serde(rename = "TerminalId")]
    pub terminal_id: String,
    #[serde(rename = "DepositorName")]
    pub depositor_name: Option<String>,
    #[serde(rename = "DepositorCode")]
    pub depositor_code: Option<String>,
    #[serde(rename = "CashDeposited", serialize_with = "serialize_f64_as_whole")]
    pub cash_deposited: f64,
    #[serde(rename = "AdvanceDeductedAmount", serialize_with = "serialize_f64_as_whole")]
    pub advance_deducted_amount: f64,
    #[serde(rename = "SettledAmount", serialize_with = "serialize_f64_as_whole")]
    pub settled_amount: f64,
    #[serde(rename = "TotalUnsettled", serialize_with = "serialize_f64_as_whole")]
    pub total_unsettled: f64,
    #[serde(rename = "TraceNr")]
    pub trace_nr: Option<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ExternalDsMultiMerchantDeposit {
    #[serde(rename = "RecordId")]
    pub record_id: i64,
    #[serde(rename = "RecordGuid")]
    pub record_guid: Option<Uuid>,
    #[serde(rename = "SubmitSettlementDate", serialize_with = "serialize_utc_as_sast")]
    pub submit_settlement_date: DateTime<Utc>,
    #[serde(rename = "DepositDateTime", serialize_with = "serialize_utc_as_sast")]
    pub deposit_date_time: DateTime<Utc>,
    #[serde(rename = "TerminalId")]
    pub terminal_id: String,
    #[serde(rename = "DepositorName")]
    pub depositor_name: Option<String>,
    #[serde(rename = "DepositorCode")]
    pub depositor_code: Option<String>,
    #[serde(rename = "CashDeposited", serialize_with = "serialize_f64_as_whole")]
    pub cash_deposited: f64,
    #[serde(rename = "SettledAmount", serialize_with = "serialize_f64_as_whole")]
    pub settled_amount: f64,
    #[serde(rename = "TotalUnsettled", serialize_with = "serialize_f64_as_whole")]
    pub total_unsettled: f64,
    #[serde(rename = "TraceNr")]
    pub trace_nr: Option<String>,
    #[serde(rename = "EntityTerminalId")]
    pub entity_terminal_id: Option<String>,
    #[serde(rename = "EntityName")]
    pub entity_name: Option<String>,
}



// Database structures for conversion
#[derive(Debug, Clone)]
pub struct DbMcaMerchantDeposit {
    pub record_id: i64,
    pub record_guid: Option<Uuid>,
    pub submit_settlement_date: NaiveDateTime,
    pub deposit_date_time: NaiveDateTime,
    pub terminal_id: String,
    pub depositor_name: Option<String>,
    pub depositor_code: Option<String>,
    pub cash_deposited: f64,
    pub advance_deducted_amount: f64,
    pub settled_amount: f64,
    pub total_unsettled: f64,
    pub trace_nr: Option<String>,
}

impl From<DbMcaMerchantDeposit> for ExternalDsMcaMerchantDeposit {
    fn from(db: DbMcaMerchantDeposit) -> Self {
        Self {
            record_id: db.record_id,
            record_guid: db.record_guid,
            submit_settlement_date: Utc.from_utc_datetime(&db.submit_settlement_date),
            deposit_date_time: Utc.from_utc_datetime(&db.deposit_date_time),
            terminal_id: db.terminal_id,
            depositor_name: db.depositor_name,
            depositor_code: db.depositor_code,
            cash_deposited: db.cash_deposited,
            advance_deducted_amount: db.advance_deducted_amount,
            settled_amount: db.settled_amount,
            total_unsettled: db.total_unsettled,
            trace_nr: db.trace_nr,
        }
    }
}

impl From<DbMcaMerchantDeposit> for ExternalDsMultiMerchantDeposit {
    fn from(db: DbMcaMerchantDeposit) -> Self {
        Self {
            record_id: db.record_id,
            record_guid: db.record_guid,
            submit_settlement_date: Utc.from_utc_datetime(&db.submit_settlement_date),
            deposit_date_time: Utc.from_utc_datetime(&db.deposit_date_time),
            terminal_id: db.terminal_id,
            depositor_name: db.depositor_name,
            depositor_code: db.depositor_code,
            cash_deposited: db.cash_deposited,
            settled_amount: db.settled_amount,
            total_unsettled: db.total_unsettled,
            trace_nr: db.trace_nr,
            entity_terminal_id: None, // TODO: Map from appropriate DB field when available
            entity_name: None, // TODO: Map from appropriate DB field when available
        }
    }
} 