// # auth.rs
//
// Authentication middleware for securing API endpoints.
// This module provides JWT-based authentication for protecting routes.

// Axum imports removed - not needed for current JWT token generation
use jsonwebtoken::{encode, Algorithm, Encoding<PERSON><PERSON>, Header};
use serde::{Deserialize, Serialize};
use std::time::{SystemTime, UNIX_EPOCH};

/// JWT Claims structure matching the external API requirements
#[derive(Debug, Serialize, Deserialize)]
pub struct JwtClaims {
    pub oi_prst: String,
    pub client_id: String,
    pub oi_tkn_id: String,
    pub aud: String,
    pub scope: String,
    pub exp: u64,
    pub iss: String,
    pub iat: u64,
}

/// Generates a signed JWT token for authenticating with external APIs
/// 
/// This creates a complete JWT token with RS256 signing that can be used
/// for Bearer authentication with external services.
pub fn generate_jwt_token() -> Result<String, Box<dyn std::error::Error>> {
    let current_timestamp = SystemTime::now()
        .duration_since(UNIX_EPOCH)?
        .as_secs();
    
    let expiration_timestamp = current_timestamp + 3600; // 1 hour from now
    
    let claims = JwtClaims {
        oi_prst: "Crm_MerchantDeposits".to_string(),
        client_id: "Crm_MerchantDeposits".to_string(),
        oi_tkn_id: "49eec5df-1356-682d-c4e1-3a1af66b1921".to_string(),
        aud: "Crm_MerchantDeposits".to_string(),
        scope: "Crm_MerchantDeposits".to_string(),
        exp: expiration_timestamp,
        iss: "http://authserver/".to_string(),
        iat: current_timestamp,
    };
    
    // Create JWT header with HS256 algorithm (compatible with secret key)
    let header = Header {
        alg: Algorithm::HS256,
        kid: Some("5D8E8003MC6566MBE90912BC646A5ADA7FF5644".to_string()),
        x5t: Some("XY6Awx1ZhvpCRK8BkalrAf_VkQ".to_string()),
        typ: Some("at+jwt".to_string()),
        ..Default::default()
    };
    
    // For now, we'll use a dummy signing key. In production, this should be a proper RSA private key
    let signing_key = std::env::var("JWT_SIGNING_KEY")
        .unwrap_or_else(|_| "dummy-key-for-development".to_string());
    
    let encoding_key = EncodingKey::from_secret(signing_key.as_ref());
    
    let token = encode(&header, &claims, &encoding_key)?;
    
    Ok(token)
}







 