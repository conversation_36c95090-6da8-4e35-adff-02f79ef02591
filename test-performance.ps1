#!/usr/bin/env pwsh
# Test script to verify performance optimizations are working

Write-Host "🚀 Testing Axum Performance Optimizations" -ForegroundColor Green
Write-Host "===========================================" -ForegroundColor Green

# Start the server in the background
Write-Host "Starting server..." -ForegroundColor Yellow
$serverProcess = Start-Process -FilePath ".\backend\target\release\myatm-embedded-ui.exe" -PassThru -NoNewWindow

# Wait for server to start
Start-Sleep -Seconds 3

Write-Host "Server started. Running tests..." -ForegroundColor Yellow

try {
    # Test 1: Basic connectivity
    Write-Host "`n🔍 Test 1: Basic connectivity" -ForegroundColor Cyan
    $response = Invoke-WebRequest -Uri "http://localhost:8000/health/live" -UseBasicParsing
    Write-Host "Status: $($response.StatusCode)" -ForegroundColor Green
    
    # Test 2: Security headers
    Write-Host "`n🔒 Test 2: Security headers" -ForegroundColor Cyan
    $headers = $response.Headers
    Write-Host "X-Content-Type-Options: $($headers['X-Content-Type-Options'])" -ForegroundColor Green
    Write-Host "X-Frame-Options: $($headers['X-Frame-Options'])" -ForegroundColor Green
    Write-Host "X-XSS-Protection: $($headers['X-XSS-Protection'])" -ForegroundColor Green
    Write-Host "X-Powered-By: $($headers['X-Powered-By'])" -ForegroundColor Green
    Write-Host "X-Request-ID: $($headers['X-Request-ID'])" -ForegroundColor Green
    
    # Test 3: Compression (using performance test endpoint)
    Write-Host "`n🗜️  Test 3: Compression test" -ForegroundColor Cyan
    $compressResponse = Invoke-WebRequest -Uri "http://localhost:8000/api/performance-test" -UseBasicParsing -Headers @{"Accept-Encoding"="gzip, deflate, br"}
    Write-Host "Status: $($compressResponse.StatusCode)" -ForegroundColor Green
    Write-Host "Content-Encoding: $($compressResponse.Headers['Content-Encoding'])" -ForegroundColor Green
    Write-Host "Content-Length: $($compressResponse.Headers['Content-Length'])" -ForegroundColor Green
    
    # Test 4: API endpoints
    Write-Host "`n📊 Test 4: API endpoints" -ForegroundColor Cyan
    $apiResponse = Invoke-WebRequest -Uri "http://localhost:8000/api/cache/version" -UseBasicParsing
    Write-Host "Cache API Status: $($apiResponse.StatusCode)" -ForegroundColor Green
    
    # Test 5: Static file serving
    Write-Host "`n📁 Test 5: Static file serving" -ForegroundColor Cyan
    $staticResponse = Invoke-WebRequest -Uri "http://localhost:8000/" -UseBasicParsing
    Write-Host "Static file Status: $($staticResponse.StatusCode)" -ForegroundColor Green
    
    Write-Host "`n✅ All tests completed successfully!" -ForegroundColor Green
    Write-Host "🎉 Performance optimizations are working:" -ForegroundColor Green
    Write-Host "  - Gzip/Brotli/Zstd compression enabled" -ForegroundColor White
    Write-Host "  - Security headers configured" -ForegroundColor White
    Write-Host "  - Request ID tracking enabled" -ForegroundColor White
    Write-Host "  - Request timeout protection (30s)" -ForegroundColor White
    Write-Host "  - Request body limit (10MB)" -ForegroundColor White
    Write-Host "  - OpenTelemetry tracing enabled" -ForegroundColor White
    
} catch {
    Write-Host "❌ Error during testing: $_" -ForegroundColor Red
} finally {
    # Clean up
    Write-Host "`n🧹 Stopping server..." -ForegroundColor Yellow
    if ($serverProcess -and !$serverProcess.HasExited) {
        $serverProcess.Kill()
        $serverProcess.WaitForExit()
    }
    Write-Host "Server stopped." -ForegroundColor Green
}

Write-Host "`n🏁 Performance test completed!" -ForegroundColor Green 