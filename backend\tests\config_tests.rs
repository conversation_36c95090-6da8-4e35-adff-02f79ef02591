// config_tests.rs

//! Configuration System Unit Tests
//! 
//! This module contains unit tests for the application configuration system used in the
//! ATM embedded UI backend. It validates the proper loading, parsing, and defaulting
//! of configuration values from environment variables and other sources.
//! 
//! ## Test Coverage:
//! 
//! ### 1. Environment Variable Loading
//! - Tests the `AppConfig::from_env()` functionality
//! - Validates that configuration can be loaded from environment variables
//! - Ensures proper error handling when environment variables are missing or invalid
//! 
//! ### 2. Default Value Validation
//! - Verifies that appropriate default values are used when environment variables are not set
//! - Tests default server configuration (port 8000)
//! - Tests default database configuration (SQLite connection string)
//! 
//! ### 3. Configuration Structure Integrity
//! - Ensures that the configuration structure is properly populated
//! - Validates that all required configuration sections are accessible
//! - Tests the relationship between different configuration components
//! 
//! ## Test Philosophy:
//! 
//! - **Isolation**: Each test runs independently without affecting others
//! - **Defaults First**: Tests verify that the application works with default settings
//! - **Environment Agnostic**: Tests don't rely on specific environment variable setup
//! - **Fail Fast**: Configuration errors should be caught early in application startup
//! 
//! ## Important Notes:
//! 
//! - **Default Behavior**: Tests assume default configuration when no environment variables are set
//! - **Database**: Uses SQLite as the default database for testing and development
//! - **Server Port**: Default port 8000 is used for local development
//! - **Production**: In production, these defaults should be overridden via environment variables
//! 
//! ## Configuration Dependencies:
//! 
//! These tests validate the configuration system defined in `src/config.rs` which includes:
//! - Server configuration (host, port, static file paths)
//! - Database configuration (connection strings, pool settings)
//! - Application-specific settings (cache busting, monitoring, etc.)
//! 
//! ## Running Tests:
//! 
//! ```bash
//! cargo test config_tests
//! ```
//! 
//! For verbose output:
//! ```bash
//! cargo test config_tests -- --nocapture
//! ```

use myatm_embedded_ui::config::*;

#[test]
fn test_config_from_env() {
    // Test with default values
    let config = AppConfig::from_env().unwrap();
    assert_eq!(config.server.port, 8000);
    assert_eq!(config.database.connection_string, "sqlite://deposits.db");
} 