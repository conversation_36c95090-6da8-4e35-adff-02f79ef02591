# Test script for Axum cache busting features
param(
    [string]$BaseUrl = "http://localhost:8000",
    [int]$Port = 8000,
    [switch]$SkipBuild = $false
)

$ErrorActionPreference = "Stop"

Write-Host "🧪 Testing Axum Cache Busting Features..." -ForegroundColor Green
Write-Host "🌐 Base URL: $BaseUrl" -ForegroundColor Cyan

# Function to make HTTP requests with error handling
function Invoke-SafeWebRequest {
    param(
        [string]$Uri,
        [string]$Method = "GET",
        [hashtable]$Headers = @{}
    )
    
    try {
        $response = Invoke-WebRequest -Uri $Uri -Method $Method -Headers $Headers -UseBasicParsing -TimeoutSec 10
        return @{
            StatusCode = $response.StatusCode
            Headers = $response.Headers
            Content = $response.Content
            Success = $true
        }
    } catch {
        return @{
            StatusCode = $_.Exception.Response.StatusCode
            Error = $_.Exception.Message
            Success = $false
        }
    }
}

# Function to test API endpoint
function Test-ApiEndpoint {
    param(
        [string]$Endpoint,
        [string]$Description,
        [scriptblock]$Validator = $null
    )
    
    Write-Host "🔍 Testing: $Description" -ForegroundColor Yellow
    $result = Invoke-SafeWebRequest -Uri "$BaseUrl$Endpoint"
    
    if ($result.Success -and $result.StatusCode -eq 200) {
        Write-Host "✅ $Description - OK" -ForegroundColor Green
        
        if ($Validator) {
            try {
                $data = $result.Content | ConvertFrom-Json
                & $Validator $data
            } catch {
                Write-Host "⚠️  Validation failed: $_" -ForegroundColor Yellow
            }
        }
        
        return $true
    } else {
        Write-Host "❌ $Description - Failed" -ForegroundColor Red
        Write-Host "   Status: $($result.StatusCode)" -ForegroundColor Red
        Write-Host "   Error: $($result.Error)" -ForegroundColor Red
        return $false
    }
}

# Function to check cache headers
function Test-CacheHeaders {
    param(
        [string]$Endpoint,
        [string]$Description,
        [string[]]$ExpectedHeaders = @()
    )
    
    Write-Host "🔍 Testing: $Description" -ForegroundColor Yellow
    $result = Invoke-SafeWebRequest -Uri "$BaseUrl$Endpoint"
    
    if ($result.Success) {
        $headersFound = @()
        $headersMissing = @()
        
        foreach ($header in $ExpectedHeaders) {
            if ($result.Headers.ContainsKey($header)) {
                $headersFound += "$header = $($result.Headers[$header])"
            } else {
                $headersMissing += $header
            }
        }
        
        if ($headersFound.Count -gt 0) {
            Write-Host "✅ $Description - Headers Found:" -ForegroundColor Green
            foreach ($header in $headersFound) {
                Write-Host "   $header" -ForegroundColor White
            }
        }
        
        if ($headersMissing.Count -gt 0) {
            Write-Host "⚠️  $Description - Missing Headers:" -ForegroundColor Yellow
            foreach ($header in $headersMissing) {
                Write-Host "   $header" -ForegroundColor White
            }
        }
        
        return $headersMissing.Count -eq 0
    } else {
        Write-Host "❌ $Description - Failed to fetch" -ForegroundColor Red
        return $false
    }
}

try {
    # Build the application if not skipped
    if (-not $SkipBuild) {
        Write-Host "🔨 Building Axum application..." -ForegroundColor Yellow
        Push-Location backend
        
        $buildResult = & cargo build --release
        if ($LASTEXITCODE -ne 0) {
            throw "Build failed"
        }
        
        Pop-Location
        Write-Host "✅ Build completed" -ForegroundColor Green
    }
    
    # Check if server is running
    Write-Host "🔍 Checking if server is running..." -ForegroundColor Yellow
    $serverRunning = $false
    try {
        $response = Invoke-WebRequest -Uri "$BaseUrl/health/live" -UseBasicParsing -TimeoutSec 5
        if ($response.StatusCode -eq 200) {
            $serverRunning = $true
            Write-Host "✅ Server is running" -ForegroundColor Green
        }
    } catch {
        Write-Host "⚠️  Server not running - you may need to start it manually" -ForegroundColor Yellow
        Write-Host "   Run: cd backend && cargo run" -ForegroundColor White
    }
    
    if (-not $serverRunning) {
        Write-Host "❌ Cannot test without running server" -ForegroundColor Red
        exit 1
    }
    
    # Test cache busting API endpoints
    Write-Host "" -ForegroundColor White
    Write-Host "🎯 Testing Cache Busting API Endpoints..." -ForegroundColor Green
    
    $tests = @(
        @{
            Endpoint = "/api/cache/version"
            Description = "Cache Version Endpoint"
            Validator = {
                param($data)
                if (-not $data.version) { throw "Missing version field" }
                if (-not $data.timestamp) { throw "Missing timestamp field" }
                Write-Host "   Version: $($data.version)" -ForegroundColor Cyan
                Write-Host "   Timestamp: $($data.timestamp)" -ForegroundColor Cyan
            }
        },
        @{
            Endpoint = "/api/cache/manifest"
            Description = "Asset Manifest Endpoint"
            Validator = {
                param($data)
                if (-not $data.version) { throw "Missing version field" }
                if (-not $data.assets) { throw "Missing assets field" }
                Write-Host "   Version: $($data.version)" -ForegroundColor Cyan
                Write-Host "   Assets: $($data.assets.Count)" -ForegroundColor Cyan
            }
        },
        @{
            Endpoint = "/api/cache/health"
            Description = "Cache Health Endpoint"
            Validator = {
                param($data)
                if (-not $data.status) { throw "Missing status field" }
                if (-not $data.cache_version) { throw "Missing cache_version field" }
                Write-Host "   Status: $($data.status)" -ForegroundColor Cyan
                Write-Host "   Cache Version: $($data.cache_version)" -ForegroundColor Cyan
            }
        },
        @{
            Endpoint = "/api/cache/refresh"
            Description = "Cache Refresh Endpoint"
            Validator = {
                param($data)
                if (-not $data.status) { throw "Missing status field" }
                if (-not $data.new_version) { throw "Missing new_version field" }
                Write-Host "   Status: $($data.status)" -ForegroundColor Cyan
                Write-Host "   New Version: $($data.new_version)" -ForegroundColor Cyan
            }
        }
    )
    
    $passedTests = 0
    foreach ($test in $tests) {
        if (Test-ApiEndpoint -Endpoint $test.Endpoint -Description $test.Description -Validator $test.Validator) {
            $passedTests++
        }
    }
    
    # Test cache headers
    Write-Host "" -ForegroundColor White
    Write-Host "🎯 Testing Cache Headers..." -ForegroundColor Green
    
    $headerTests = @(
        @{
            Endpoint = "/"
            Description = "Index Page Cache Headers"
            ExpectedHeaders = @("X-Cache-Version", "Cache-Control")
        },
        @{
            Endpoint = "/app.js"
            Description = "JavaScript File Cache Headers"
            ExpectedHeaders = @("X-Cache-Version", "Cache-Control", "Content-Type")
        },
        @{
            Endpoint = "/health/live"
            Description = "Health Endpoint Cache Headers"
            ExpectedHeaders = @("X-Cache-Version")
        }
    )
    
    $passedHeaderTests = 0
    foreach ($test in $headerTests) {
        if (Test-CacheHeaders -Endpoint $test.Endpoint -Description $test.Description -ExpectedHeaders $test.ExpectedHeaders) {
            $passedHeaderTests++
        }
    }
    
    # Test static file serving
    Write-Host "" -ForegroundColor White
    Write-Host "🎯 Testing Static File Serving..." -ForegroundColor Green
    
    $staticTests = @(
        @{ Endpoint = "/"; Description = "Index Page" },
        @{ Endpoint = "/app.js"; Description = "JavaScript File" },
        @{ Endpoint = "/cache-client.js"; Description = "Cache Client File" }
    )
    
    $passedStaticTests = 0
    foreach ($test in $staticTests) {
        if (Test-ApiEndpoint -Endpoint $test.Endpoint -Description $test.Description) {
            $passedStaticTests++
        }
    }
    
    # Summary
    Write-Host "" -ForegroundColor White
    Write-Host "📊 Test Results Summary:" -ForegroundColor Green
    Write-Host "   API Endpoints: $passedTests/$($tests.Count)" -ForegroundColor White
    Write-Host "   Cache Headers: $passedHeaderTests/$($headerTests.Count)" -ForegroundColor White
    Write-Host "   Static Files: $passedStaticTests/$($staticTests.Count)" -ForegroundColor White
    
    $totalTests = $tests.Count + $headerTests.Count + $staticTests.Count
    $totalPassed = $passedTests + $passedHeaderTests + $passedStaticTests
    
    Write-Host "   Total: $totalPassed/$totalTests" -ForegroundColor White
    
    if ($totalPassed -eq $totalTests) {
        Write-Host "🎉 All tests passed!" -ForegroundColor Green
    } else {
        Write-Host "⚠️  Some tests failed. Check the logs above." -ForegroundColor Yellow
    }
    
    # Additional information
    Write-Host "" -ForegroundColor White
    Write-Host "🔍 Additional Information:" -ForegroundColor Green
    Write-Host "   - Cache version API: $BaseUrl/api/cache/version" -ForegroundColor White
    Write-Host "   - Asset manifest API: $BaseUrl/api/cache/manifest" -ForegroundColor White
    Write-Host "   - Cache refresh API: $BaseUrl/api/cache/refresh" -ForegroundColor White
    Write-Host "   - Cache health API: $BaseUrl/api/cache/health" -ForegroundColor White
    Write-Host "   - Check browser console for client-side cache busting" -ForegroundColor White
    
} catch {
    Write-Host "❌ Test failed: $_" -ForegroundColor Red
    exit 1
} 