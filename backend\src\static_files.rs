// static_files.rs

//! Static File Server with Cache Busting and SPA Support
//! 
//! This module provides comprehensive static file serving capabilities for the ATM embedded UI
//! web application. It implements several critical features for modern web applications:
//! 
//! ## Core Functionality:
//! 
//! ### 1. Cache Busting System
//! - Implements version-based cache busting using build timestamps or custom versions
//! - Automatically appends version parameters (?v=version) to static assets
//! - Maintains an asset manifest for tracking versioned files
//! - Provides strong caching (30 days) for versioned assets and shorter caching for unversioned
//! 
//! ### 2. Static File Serving
//! - Serves files from the ./static directory with proper MIME types
//! - Implements security checks to prevent directory traversal attacks
//! - Provides optimized HTTP headers for different file types (JS, CSS, HTML, images)
//! - Supports ETag-based caching for better performance
//! 
//! ### 3. SPA (Single Page Application) Support
//! - Implements fallback routing for client-side navigation
//! - Serves index.html for non-API routes that don't match static files
//! - Allows React/Vue/Angular style routing to work correctly
//! 
//! ### 4. Specialized Handlers
//! - Custom handlers for app.js and cache-client.js with optimal caching
//! - Dynamic index.html injection with cache-busted asset URLs
//! - Content-type detection and appropriate HTTP headers
//! 
//! ## Important Notes:
//! 
//! - **Security**: All file paths are validated to prevent access outside ./static directory
//! - **Performance**: Uses immutable caching for versioned assets (30 days max-age)
//! - **Development**: Unversioned files use shorter cache times (5 minutes) for easier development
//! - **Build Integration**: Cache busting version is set at application startup via init_cache_busting()
//! - **Production Ready**: Includes proper ETags, cache headers, and MIME type detection
//! 
//! ## Usage:
//! 
//! Initialize cache busting at application startup:
//! ```rust
//! init_cache_busting(Some("1.2.3".to_string()));
//! ```
//! 
//! Then register the handlers with your Axum router:
//! ```rust
//! .route("/static/*path", get(serve_static_file))
//! .route("/app.js", get(serve_app_js))
//! .route("/cache-client.js", get(serve_cache_client_js))
//! .fallback(serve_spa_fallback)
//! ```

use axum::{
    extract::Path,
    http::{header, HeaderMap, HeaderValue, StatusCode, Uri},
    response::{IntoResponse, Response},
};
use std::{
    collections::HashMap,
    fs,
    path::PathBuf,
    sync::OnceLock,
    time::{SystemTime, UNIX_EPOCH},
};


/// Build version for cache busting (set at compile time)
static BUILD_VERSION: OnceLock<String> = OnceLock::new();

/// Asset manifest for cache busting
static ASSET_MANIFEST: OnceLock<HashMap<String, String>> = OnceLock::new();

/// Initialize cache busting with version
pub fn init_cache_busting(version: Option<String>) {
    let version = version.unwrap_or_else(|| {
        // Use build timestamp if no version provided
        SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .unwrap()
            .as_secs()
            .to_string()
    });
    
    BUILD_VERSION.set(version.clone()).ok();
    
    // Generate asset manifest
    let mut manifest = HashMap::new();
    if let Ok(entries) = fs::read_dir("./static") {
        for entry in entries.flatten() {
            if let Some(name) = entry.file_name().to_str() {
                if name.ends_with(".js") || name.ends_with(".css") {
                    manifest.insert(name.to_string(), format!("{}?v={}", name, version));
                }
            }
        }
    }
    
    ASSET_MANIFEST.set(manifest).ok();
}

/// Get the current build version
pub fn get_build_version() -> String {
    BUILD_VERSION.get().cloned().unwrap_or_else(|| "unknown".to_string())
}

/// Get asset URL with cache busting
#[allow(dead_code)]
pub fn get_asset_url(asset_name: &str) -> String {
    ASSET_MANIFEST
        .get()
        .and_then(|manifest| manifest.get(asset_name))
        .cloned()
        .unwrap_or_else(|| format!("{}?v={}", asset_name, get_build_version()))
}

/// Get a clone of the asset manifest
pub fn get_asset_manifest() -> HashMap<String, String> {
    ASSET_MANIFEST.get().cloned().unwrap_or_default()
}

/// Custom static file handler with cache busting (for /static/* paths)
pub async fn serve_static_file(Path(path): Path<String>) -> Response {
    let file_path = PathBuf::from("./static").join(&path);
    serve_file_with_headers(&file_path, &path).await
}

/// Common file serving logic with proper headers
async fn serve_file_with_headers(file_path: &PathBuf, path: &str) -> Response {
    // Security check - ensure path is within static directory
    if !file_path.starts_with("./static") {
        return (StatusCode::FORBIDDEN, "Access denied").into_response();
    }
    
    // Check if file exists
    if !file_path.exists() {
        return (StatusCode::NOT_FOUND, "File not found").into_response();
    }
    
    // Read file content
    let content = match fs::read(&file_path) {
        Ok(content) => content,
        Err(_) => return (StatusCode::INTERNAL_SERVER_ERROR, "Failed to read file").into_response(),
    };
    
    // Determine content type
    let content_type = match file_path.extension().and_then(|ext| ext.to_str()) {
        Some("html") => "text/html; charset=utf-8",
        Some("js") => "application/javascript; charset=utf-8",
        Some("css") => "text/css; charset=utf-8",
        Some("json") => "application/json; charset=utf-8",
        Some("png") => "image/png",
        Some("jpg") | Some("jpeg") => "image/jpeg",
        Some("gif") => "image/gif",
        Some("svg") => "image/svg+xml",
        Some("ico") => "image/x-icon",
        _ => "application/octet-stream",
    };
    
    // Build headers
    let mut headers = HeaderMap::new();
    headers.insert(header::CONTENT_TYPE, HeaderValue::from_static(content_type));
    
    // Add cache control headers based on file type
    if path.ends_with(".js") || path.ends_with(".css") {
        // For JS/CSS files, use strong caching if version parameter is present
        if path.contains("?v=") || path.contains("-") {  // Hashed files
            headers.insert(
                header::CACHE_CONTROL,
                HeaderValue::from_static("public, max-age=2592000, immutable"), // 30 days
            );
        } else {
            headers.insert(
                header::CACHE_CONTROL,
                HeaderValue::from_static("public, max-age=300"), // 5 minutes
            );
        }
    } else if path.ends_with(".html") {
        // HTML files should not be cached aggressively
        headers.insert(
            header::CACHE_CONTROL,
            HeaderValue::from_static("public, max-age=0, must-revalidate"),
        );
    } else {
        // Other static assets
        headers.insert(
            header::CACHE_CONTROL,
            HeaderValue::from_static("public, max-age=86400"), // 1 day
        );
    }
    
    // Add ETag for better caching
    let version = get_build_version();
    let etag = format!("\"{}\"", version);
    headers.insert(header::ETAG, HeaderValue::from_str(&etag).unwrap());
    
    // Add custom cache busting header
    headers.insert(
        "X-Cache-Version",
        HeaderValue::from_str(&version).unwrap(),
    );
    
    (headers, content).into_response()
}

/// Serve index.html with injected cache busting
pub async fn serve_index() -> Response {
    let index_path = PathBuf::from("./static/index.html");
    
    if !index_path.exists() {
        return (StatusCode::NOT_FOUND, "index.html not found").into_response();
    }
    
    let content = match fs::read_to_string(&index_path) {
        Ok(content) => content,
        Err(_) => return (StatusCode::INTERNAL_SERVER_ERROR, "Failed to read index.html").into_response(),
    };
    
    // Inject cache busting version if not already present
    let version = get_build_version();
    let processed_content = if content.contains("?v=") {
        content // Already has version parameters
    } else {
        // Add version parameters to JS and CSS files
        content
            .replace(".js\"", &format!(".js?v={}\"", version))
            .replace(".js'", &format!(".js?v={}'", version))
            .replace(".css\"", &format!(".css?v={}\"", version))
            .replace(".css'", &format!(".css?v={}'", version))
    };
    
    // Build headers
    let mut headers = HeaderMap::new();
    headers.insert(header::CONTENT_TYPE, HeaderValue::from_static("text/html; charset=utf-8"));
    headers.insert(
        header::CACHE_CONTROL,
        HeaderValue::from_static("public, max-age=0, must-revalidate"),
    );
    headers.insert(
        "X-Cache-Version",
        HeaderValue::from_str(&version).unwrap(),
    );
    
    (headers, processed_content).into_response()
}

/// Serve app.js with cache busting
pub async fn serve_app_js() -> Response {
    serve_js_file("app.js").await
}

/// Serve cache-client.js with cache busting
pub async fn serve_cache_client_js() -> Response {
    serve_js_file("cache-client.js").await
}

/// Helper function to serve JS files with cache busting
async fn serve_js_file(filename: &str) -> Response {
    let file_path = PathBuf::from("./static").join(filename);
    
    // Check if file exists
    if !file_path.exists() {
        return (StatusCode::NOT_FOUND, format!("{} not found", filename)).into_response();
    }
    
    // Read file content
    let content = match fs::read(&file_path) {
        Ok(content) => content,
        Err(_) => return (StatusCode::INTERNAL_SERVER_ERROR, format!("Failed to read {}", filename)).into_response(),
    };
    
    // Build headers with cache busting
    let mut headers = HeaderMap::new();
    headers.insert(header::CONTENT_TYPE, HeaderValue::from_static("application/javascript; charset=utf-8"));
    
    // Use strong caching with version parameter (browsers will include ?v= parameter)
    headers.insert(
        header::CACHE_CONTROL,
        HeaderValue::from_static("public, max-age=2592000, immutable"), // 30 days
    );
    
    // Add ETag for better caching
    let version = get_build_version();
    let etag = format!("\"{}\"", version);
    headers.insert(header::ETAG, HeaderValue::from_str(&etag).unwrap());
    
    // Add custom cache busting header
    headers.insert(
        "X-Cache-Version",
        HeaderValue::from_str(&version).unwrap(),
    );
    
    (headers, content).into_response()
}



/// SPA fallback handler - serves index.html for all non-API routes
/// This allows client-side routing to work properly
pub async fn serve_spa_fallback(uri: Uri) -> Response {
    let path = uri.path();
    
    // If it's a request for a static file (JS, CSS, HTML, images, etc.), try to serve it first
    if path.ends_with(".js") || path.ends_with(".css") || path.ends_with(".html") || path.ends_with(".ico") || 
       path.ends_with(".png") || path.ends_with(".jpg") || path.ends_with(".jpeg") || 
       path.ends_with(".gif") || path.ends_with(".svg") || path.ends_with(".woff") || 
       path.ends_with(".woff2") || path.ends_with(".ttf") || path.ends_with(".eot") {
        
        // Remove leading slash for file path
        let file_path = path.trim_start_matches('/');
        let static_file_path = PathBuf::from("./static").join(file_path);
        
        // Check if the file exists and serve it
        if static_file_path.exists() {
            return serve_file_with_headers(&static_file_path, file_path).await;
        }
    }
    
    // For all other routes (SPA routes), serve index.html
    serve_index().await
} 