// # main.js
//
// Frontend Application Entry Point
//
// This is the main entry point for the MyATM Embedded UI frontend application.
// It orchestrates the loading of all necessary styles, utilities, and application
// components in the correct order.
//
// ## Loading Sequence
// 1. **Styles**: Global and feature-specific CSS files
// 2. **Cache Client**: Service worker and cache management utilities
// 3. **Router**: SPA routing system for client-side navigation
// 4. **App**: Main application logic and component initialization
//
// ## Architecture
// The application follows a modular, component-based architecture where:
// - Each feature (deposits, etc.) has its own module directory
// - Styles are loaded hierarchically (global -> feature-specific)
// - The router handles SPA navigation without page reloads
// - The app module manages page registration and routing setup
//
// ## Global Exports
// Exposes `window.myAtmApp` object for debugging and external integration.
//
// ## Build Process
// This file serves as the entry point for the build system, which:
// - Bundles all imports into optimized assets
// - Applies cache-busting for production deployments
// - Minifies and compresses the final output

// Import global styles first
import './styles.css';

// Import feature-specific styles
// import './deposits/deposits.css'; // Removed: deposits.css will be loaded only with deposits.html

// Import cache management utilities
import './cache-client.js';

// Import SPA router for client-side navigation
import './router.js';

// Import main application logic
import './app.js';

// Export application metadata for global access
if (typeof window !== 'undefined') {
    window.myAtmApp = {
        version: '1.0.0',
        initialized: true,
        features: ['deposits', 'cache-management', 'spa-routing']
    };
} 