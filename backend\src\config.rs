// # config.rs
//
// Configuration management for the application.
// This module handles loading configuration from environment variables.

use serde::{Deserialize, Serialize};
use std::env;

use crate::deposits::DepositsConfig;

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct AppConfig {
    pub server: ServerConfig,
    pub database: DatabaseConfig,
    pub deposits: DepositsConfig,
    pub auth: AuthConfig,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct ServerConfig {
    pub port: u16,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DatabaseConfig {
    pub connection_string: String,
}



#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct AuthConfig {
    pub jwt_secret: String,
    pub jwt_signing_key: Option<String>,
}

impl AppConfig {
    pub fn from_env() -> Result<Self, Box<dyn std::error::Error>> {
        Ok(AppConfig {
            server: ServerConfig {
                port: env::var("PORT")
                    .unwrap_or_else(|_| "8000".to_string())
                    .parse()
                    .unwrap_or(8000),
            },
            database: DatabaseConfig {
                connection_string: env::var("DATABASE_URL")
                    .unwrap_or_else(|_| "sqlite://deposits.db".to_string()),
            },
            deposits: DepositsConfig::from_env()?,
            auth: AuthConfig {
                jwt_secret: env::var("JWT_SECRET")
                    .unwrap_or_else(|_| "your-secret-key".to_string()),
                jwt_signing_key: env::var("JWT_SIGNING_KEY").ok(),
            },
        })
    }
}

 