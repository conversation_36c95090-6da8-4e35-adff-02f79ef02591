// # monitoring.rs
//
// OpenTelemetry-based observability stack setup and configuration.
// This module configures comprehensive monitoring including distributed tracing,
// metrics collection, structured logging, and optional Elasticsearch log shipping.
//
// Key design choices:
// - Uses OpenTelemetry for vendor-neutral observability with OTLP export
// - Combines multiple tracing layers: OTLP for spans, JSON for logs, optional Elasticsearch
// - Includes system metrics collection (CPU, memory) for infrastructure monitoring
// - Provides environment-based configuration with development-friendly defaults
// - Uses structured logging with proper correlation between traces, logs, and metrics
// - Supports both local OTLP collector and production observability infrastructure

use crate::elasticsearch_layer::ElasticsearchLayer;
use opentelemetry::{global, KeyValue};
use opentelemetry_otlp::WithExportConfig;
use opentelemetry_sdk::{runtime, trace as sdktrace, Resource};
use std::time::Duration;
use sysinfo::{Pid, System};
use tracing::{error, info};
use tracing_subscriber::layer::SubscriberExt;
use tracing_subscriber::{EnvFilter, Registry};

const SERVICE_NAME: &str = "myatm-embedded-ui";

/// Determines the deployment environment from environment variables.
/// 
/// Checks multiple environment variable names to support different
/// deployment patterns and provides a sensible default for development.
fn get_deployment_environment() -> String {
    std::env::var("DEPLOYMENT_ENVIRONMENT")
        .or_else(|_| std::env::var("ENVIRONMENT"))
        .unwrap_or_else(|_| "development".to_string())
}

/// Initializes the tracing subsystem with multiple layers for comprehensive observability.
/// 
/// Sets up a layered tracing architecture that combines:
/// - OpenTelemetry tracing for distributed trace collection
/// - JSON-formatted structured logging to stdout
/// - Optional Elasticsearch layer for direct log shipping
/// 
/// This provides complete observability coverage for production environments
/// while remaining development-friendly with fallbacks when external systems are unavailable.
pub fn init_tracing(log_level: &str) {
    let deployment_env = get_deployment_environment();

    // Safely resolve the hostname to use for service.instance.id, host.name, and service.node.name
    let hostname = std::env::var("HOSTNAME").unwrap_or_else(|_| "unknown-host".to_string());

    // Create the EnvFilter with the specified log level
    let env_filter = EnvFilter::try_from_default_env()
        .unwrap_or_else(|_| EnvFilter::new(log_level));

    // 1. OTLP tracer for spans
    let tracer = opentelemetry_otlp::new_pipeline()
        .tracing()
        .with_exporter(
            opentelemetry_otlp::new_exporter()
                .http()
                .with_endpoint("http://localhost:4318/v1/traces"),
        )
        .with_trace_config(sdktrace::config().with_resource(Resource::new(vec![
            KeyValue::new("service.name", SERVICE_NAME),
            KeyValue::new("deployment.environment", deployment_env),
            KeyValue::new("service.instance.id", hostname.clone()),
            KeyValue::new("host.name", hostname.clone()),
            KeyValue::new("service.node.name", hostname),
        ])))
        .install_batch(runtime::Tokio)
        .expect("Failed to initialise OpenTelemetry tracer");

    let trace_layer = tracing_opentelemetry::layer().with_tracer(tracer);

    // 2. Stdout formatter for logs
    let fmt_layer = tracing_subscriber::fmt::layer()
        .json()
        .with_writer(std::io::stdout);

    // 3. Elasticsearch layer for direct log shipping
    let elasticsearch_layer = match create_elasticsearch_layer() {
        Ok(layer) => Some(layer),
        Err(e) => {
            error!("Failed to create Elasticsearch layer: {}", e);
            None
        }
    };

    // Build subscriber with all layers and env filter
    if let Some(es_layer) = elasticsearch_layer {
        let subscriber = Registry::default()
            .with(env_filter)
            .with(trace_layer)
            .with(fmt_layer)
            .with(es_layer);

        tracing::subscriber::set_global_default(subscriber)
            .expect("Unable to set global subscriber");

        info!("Elasticsearch logging layer initialized");
    } else {
        let subscriber = Registry::default()
            .with(env_filter)
            .with(trace_layer)
            .with(fmt_layer);

        tracing::subscriber::set_global_default(subscriber)
            .expect("Unable to set global subscriber");
    }

    info!("Tracing initialized with log level: {}", log_level);
}

/// Creates an Elasticsearch layer for direct log shipping from environment configuration.
/// 
/// Attempts to create an Elasticsearch logging layer using environment variables
/// for configuration. This enables direct log shipping to Elasticsearch clusters
/// for centralized log aggregation and analysis in production environments.
fn create_elasticsearch_layer(
) -> Result<ElasticsearchLayer, Box<dyn std::error::Error + Send + Sync>> {
    let elasticsearch_url = std::env::var("ELASTICSEARCH_URL")
        .map_err(|_| "ELASTICSEARCH_URL environment variable is required")?;

    let index_prefix =
        std::env::var("ELASTICSEARCH_INDEX_PREFIX").unwrap_or_else(|_| "myatm-logs".to_string());

    let service_name = std::env::var("SERVICE_NAME").unwrap_or_else(|_| SERVICE_NAME.to_string());

    info!(
        "Initializing Elasticsearch layer with URL: {}",
        elasticsearch_url
    );

    if std::env::var("ELASTICSEARCH_APIKEY").is_ok() {
        info!("Using basic authentication for Elasticsearch (decoded from base64)");
    } else {
        info!("Using unauthenticated connection to Elasticsearch");
    }

    ElasticsearchLayer::new(&elasticsearch_url, &index_prefix, &service_name)
}

/// Initializes OpenTelemetry metrics collection with OTLP export.
/// 
/// Sets up metrics collection pipeline that exports to an OTLP collector,
/// providing quantitative observability data for monitoring application
/// performance, throughput, and custom business metrics.
pub fn init_metrics() -> Result<(), Box<dyn std::error::Error>> {
    let deployment_env = get_deployment_environment();

    // Safely resolve the hostname to use for host.name and service.instance.id
    let hostname = std::env::var("HOSTNAME").unwrap_or_else(|_| "unknown-host".to_string());

    let exporter = opentelemetry_otlp::new_exporter()
        .http()
        .with_endpoint("http://localhost:4318/v1/metrics");

    let reader = opentelemetry_otlp::new_pipeline()
        .metrics(runtime::Tokio)
        .with_exporter(exporter)
        .with_resource(Resource::new(vec![
            KeyValue::new("service.name", SERVICE_NAME),
            KeyValue::new("deployment.environment", deployment_env),
            KeyValue::new("service.instance.id", hostname.clone()),
            KeyValue::new("host.name", hostname.clone()),
            KeyValue::new("service.node.name", hostname),
        ]))
        .with_period(Duration::from_secs(10))
        .build()?;

    global::set_meter_provider(reader);

    Ok(())
}

/// Logs startup information about the observability configuration.
/// 
/// Provides visibility into which observability components were successfully
/// initialized, helping with debugging observability setup issues.
pub fn log_startup_info() {
    let deployment_env = get_deployment_environment();

    info!("OpenTelemetry tracing initialized");
    info!("OpenTelemetry metrics initialized");
    info!("Service: {}", SERVICE_NAME);
    info!("Environment: {}", deployment_env);
}

/// Starts background system metrics collection for infrastructure monitoring.
/// 
/// Collects and exports system-level metrics including CPU usage, memory
/// consumption, and process-specific metrics. This provides essential
/// infrastructure observability for capacity planning and performance monitoring.
pub async fn start_system_metrics_collection() {
    use std::sync::{Arc, Mutex};

    let meter = global::meter("system_metrics");

    let cpu_usage = meter
        .f64_observable_gauge("system_cpu_usage_percent")
        .with_description("CPU usage percentage")
        .init();

    let memory_usage = meter
        .u64_observable_gauge("system_memory_usage_bytes")
        .with_description("Memory usage in bytes")
        .init();

    let memory_total = meter
        .u64_observable_gauge("system_memory_total_bytes")
        .with_description("Total memory in bytes")
        .init();

    let process_memory = meter
        .u64_observable_gauge("process_memory_usage_bytes")
        .with_description("Process memory usage in bytes")
        .init();

    let current_pid = std::process::id();

    let sys_cpu = Arc::new(Mutex::new(System::new_all()));
    let sys_memory = Arc::new(Mutex::new(System::new_all()));

    {
        let sys_clone = Arc::clone(&sys_cpu);
        let _ = meter.register_callback(&[cpu_usage.as_any()], move |observer| {
            if let Ok(mut sys) = sys_clone.lock() {
                sys.refresh_cpu();
                let val = sys.global_cpu_info().cpu_usage() as f64;
                observer.observe_f64(&cpu_usage, val, &[]);
            }
        });
    }

    {
        let sys_clone = Arc::clone(&sys_memory);
        let _ = meter.register_callback(
            &[
                memory_usage.as_any(),
                memory_total.as_any(),
                process_memory.as_any(),
            ],
            move |observer| {
                if let Ok(mut sys) = sys_clone.lock() {
                    sys.refresh_memory();
                    sys.refresh_processes();
                    observer.observe_u64(&memory_usage, sys.used_memory(), &[]);
                    observer.observe_u64(&memory_total, sys.total_memory(), &[]);
                    if let Some(proc) = sys.process(Pid::from(current_pid as usize)) {
                        observer.observe_u64(&process_memory, proc.memory() * 1024, &[]);
                    }
                }
            },
        );
    }

    info!("System metrics collection started");
}

/// Instruments for HTTP application metrics
#[allow(dead_code)]
// Function reserved for future HTTP metrics instrumentation and performance monitoring
pub fn create_app_metrics() -> (
    opentelemetry::metrics::Counter<u64>,
    opentelemetry::metrics::Histogram<f64>,
) {
    let meter = global::meter("app_metrics");

    let request_counter = meter
        .u64_counter("http_requests_total")
        .with_description("Total number of HTTP requests")
        .init();

    let request_duration = meter
        .f64_histogram("http_request_duration_seconds")
        .with_description("HTTP request duration in seconds")
        .init();

    (request_counter, request_duration)
}

/// Initialise everything
pub async fn setup_monitoring() -> Result<(), Box<dyn std::error::Error>> {
    // Default to info level if no config is provided yet
    let log_level = std::env::var("LOG_LEVEL").unwrap_or_else(|_| "info".to_string());
    init_tracing(&log_level);

    if let Err(e) = init_metrics() {
        error!("Failed to initialize metrics: {}", e);
        return Err(e);
    }

    log_startup_info();
    tokio::spawn(start_system_metrics_collection());

    Ok(())
} 