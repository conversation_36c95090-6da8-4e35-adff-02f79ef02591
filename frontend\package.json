{"name": "myatm-embedded-ui-frontend", "version": "1.0.0", "description": "Frontend for myATM embedded UI", "scripts": {"build": "node build.js", "build:dev": "node build.js --dev", "build:watch": "node build.js --watch", "clean": "rm -rf dist"}, "devDependencies": {"esbuild": "^0.20.0", "esbuild-plugin-copy": "^2.1.1"}, "private": true, "dependencies": {"@syncfusion/ej2-base": "^30.1.38", "@syncfusion/ej2-calendars": "^30.1.37", "@syncfusion/ej2-data": "^30.1.38", "@syncfusion/ej2-grids": "^30.1.39"}}