/* Deposits page styles */
.page-container {
    padding: 0;
    margin: 0;
    width: 100%;
}

.page-header {
    margin-bottom: 20px;
    border-bottom: 1px solid #e0e0e0;
    padding-bottom: 15px;
}

.page-header h1 {
    margin: 0;
    color: #333;
    font-size: 24px;
}

.breadcrumb {
    margin-top: 8px;
    color: #666;
}

.breadcrumb a {
    color: #007bff;
    text-decoration: none;
}

.breadcrumb a:hover {
    text-decoration: underline;
}

.filters-container {
    padding: 0;
    margin-bottom: 0;
    margin-left: 420px; /* This gives the calendar control a bit of margin */
}

.filter-group {
    display: flex;
    gap: 20px;
    align-items: end;
    flex-wrap: nowrap;
}

.filter-item {
    display: flex;
    flex-direction: column;
    min-width: 200px;
}

#datePicker {
    margin-left: 14px;
}

.filter-item label {
    font-weight: 500;
    margin-bottom: 5px;
    color: #495057;
}

.time-inputs {
    display: flex;
    align-items: center;
    gap: 10px;
}

.time-inputs label {
    font-weight: 700;
    font-size: 12px;
    color: #212529;
    white-space: nowrap;
    margin-bottom: 0;
}

.grid-container {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.loading-indicator {
    text-align: center;
    padding: 40px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.spinner {
    border: 4px solid #f3f3f3;
    border-top: 4px solid #007bff;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    animation: spin 1s linear infinite;
    margin: 0 auto 15px;
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }

    100% {
        transform: rotate(360deg);
    }
}

.error-message {
    background: #f8d7da;
    color: #721c24;
    padding: 20px;
    border-radius: 8px;
    border: 1px solid #f5c6cb;
    margin: 20px 0;
}

.error-content h3 {
    margin: 0 0 10px 0;
    color: #721c24;
}

.error-content button {
    background: #dc3545;
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 4px;
    cursor: pointer;
    margin-top: 10px;
}

.error-content button:hover {
    background: #c82333;
}

/* Responsive design */
@media (max-width: 768px) {
    .filter-group {
        flex-direction: column;
        align-items: stretch;
        flex-wrap: wrap;
    }

    .filter-item {
        min-width: auto;
    }

    .page-container {
        padding: 10px;
    }
}

/* Custom grid styling */
.e-grid {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.e-grid .e-headercell {
    font-weight: 600;
    color: #495057;
}

.e-grid .e-row:hover {
    background: #f5f5f5;
}

.e-grid .e-toolbar {
    border: 0;
    border-top: 1px solid #e0e0e0;
    border-radius: 0;
}

/* Grid indicators */
.grid-indicator {
    display: inline-block;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    margin-right: 5px;
}

.grid-indicator.success {
    background: #28a745;
}

.grid-indicator.warning {
    background: #ffc107;
}

.grid-indicator.error {
    background: #dc3545;
}

.deposit-type-indicator {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 24px;
    height: 24px;
    border-radius: 4px;
    background: transparent;
}

.deposit-type-indicator:hover {
    background: #f8f9fa;
    border-radius: 4px;
}

.amount-indicator {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    font-size: 12px;
    font-weight: bold;
    color: white;
}

.amount-indicator.match {
    background: #28a745;
}

.amount-indicator.mismatch {
    background: #ff5733;
}

/* Used for the aggregate totals */
.e-grid td.e-summarycell.e-templatecell.e-rightalign.validcaptioncell {
    color: #28a745 !important;  /* Bootstrap green color */
    font-weight: bold !important;
}

.e-grid td.e-summarycell.e-templatecell.e-rightalign.invalidcaptioncell {
    color: #ff5733 !important;  /* Bootstrap red color */
    font-weight: bold !important;
}

/* Capital Express Deduction caption styling for MCA mode */
.capital-express-deduction-caption {
    color: rgb(144, 53, 121) !important;
    font-weight: bold !important;
}