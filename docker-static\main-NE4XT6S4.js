/* myATM Embedded UI - Built 2025-07-04T11:59:25.664Z - Version test123 */
var MyAtmApp=(()=>{var l=class{constructor(){this.currentVersion=null,this.assetManifest=null,this.versionCheckInterval=null,this.baseUrl=window.location.origin}async init(e={}){let{checkInterval:t=3e4,autoRefresh:n=!0}=e;try{await this.fetchVersionInfo(),t>0&&(this.versionCheckInterval=setInterval(async()=>{await this.checkForUpdates(n)},t)),console.log("\u{1F527} Cache busting client initialized",{version:this.currentVersion,checkInterval:t,autoRefresh:n})}catch(a){console.error("\u274C Failed to initialize cache busting client:",a)}}async fetchVersionInfo(){try{let e=await fetch(`${this.baseUrl}/api/cache/version`);if(!e.ok)throw new Error(`HTTP ${e.status}`);let t=await e.json();return this.currentVersion=t.version,this.assetManifest=t.assets,t}catch(e){throw console.error("\u274C Failed to fetch version info:",e),e}}async fetchAssetManifest(){try{let e=await fetch(`${this.baseUrl}/api/cache/manifest`);if(!e.ok)throw new Error(`HTTP ${e.status}`);let t=await e.json();return this.assetManifest=t.assets,t}catch(e){throw console.error("\u274C Failed to fetch asset manifest:",e),e}}async checkForUpdates(e=!1){try{let t=this.currentVersion;await this.fetchVersionInfo(),t&&t!==this.currentVersion&&(console.log("\u{1F504} New version detected:",{old:t,new:this.currentVersion}),window.dispatchEvent(new CustomEvent("cacheVersionUpdated",{detail:{oldVersion:t,newVersion:this.currentVersion,assetManifest:this.assetManifest}})),e&&this.refreshPage())}catch(t){console.error("\u274C Failed to check for updates:",t)}}async refreshCache(){try{let e=await fetch(`${this.baseUrl}/api/cache/refresh`);if(!e.ok)throw new Error(`HTTP ${e.status}`);let t=await e.json();return console.log("\u{1F504} Cache refreshed:",t),await this.fetchVersionInfo(),t}catch(e){throw console.error("\u274C Failed to refresh cache:",e),e}}getAssetUrl(e){return this.assetManifest&&this.assetManifest[e]?this.assetManifest[e].url||`${e}?v=${this.currentVersion}`:this.currentVersion?`${e}?v=${this.currentVersion}`:e}async loadScript(e,t={}){let{id:n=null,async:a=!0,defer:o=!1,type:h="text/javascript"}=t;return new Promise((r,c)=>{if(n){let d=document.getElementById(n);d&&d.remove()}let s=document.createElement("script");s.src=this.getAssetUrl(e),s.async=a,s.defer=o,s.type=h,n&&(s.id=n),s.onload=()=>{console.log("\u2705 Script loaded:",s.src),r()},s.onerror=d=>{console.error("\u274C Script failed to load:",s.src,d),c(d)},document.head.appendChild(s)})}async loadCSS(e,t={}){let{id:n=null,media:a="all"}=t;return new Promise((o,h)=>{if(n){let c=document.getElementById(n);c&&c.remove()}let r=document.createElement("link");r.rel="stylesheet",r.href=this.getAssetUrl(e),r.media=a,n&&(r.id=n),r.onload=()=>{console.log("\u2705 CSS loaded:",r.href),o()},r.onerror=c=>{console.error("\u274C CSS failed to load:",r.href,c),h(c)},document.head.appendChild(r)})}refreshPage(){console.log("\u{1F504} Refreshing page for cache update..."),window.location.reload()}getCurrentVersion(){return this.currentVersion}getAssetManifest(){return this.assetManifest}showUpdateNotification(e={}){let{message:t="A new version is available. Refresh to update?",autoHide:n=!1,hideDelay:a=5e3}=e,o=document.createElement("div");o.id="cache-update-notification",o.innerHTML=`
            <div style="
                position: fixed;
                top: 20px;
                right: 20px;
                background: #4CAF50;
                color: white;
                padding: 15px 20px;
                border-radius: 5px;
                box-shadow: 0 2px 10px rgba(0,0,0,0.2);
                z-index: 10000;
                font-family: sans-serif;
                font-size: 14px;
                max-width: 300px;
            ">
                <div style="margin-bottom: 10px;">${t}</div>
                <button id="refresh-btn" style="
                    background: white;
                    color: #4CAF50;
                    border: none;
                    padding: 5px 10px;
                    border-radius: 3px;
                    cursor: pointer;
                    margin-right: 10px;
                ">Refresh</button>
                <button id="dismiss-btn" style="
                    background: transparent;
                    color: white;
                    border: 1px solid white;
                    padding: 5px 10px;
                    border-radius: 3px;
                    cursor: pointer;
                ">Dismiss</button>
            </div>
        `,document.body.appendChild(o),document.getElementById("refresh-btn").addEventListener("click",()=>{this.refreshPage()}),document.getElementById("dismiss-btn").addEventListener("click",()=>{o.remove()}),n&&setTimeout(()=>{o.parentNode&&o.remove()},a)}stop(){this.versionCheckInterval&&(clearInterval(this.versionCheckInterval),this.versionCheckInterval=null)}};window.CacheBustingClient=l;window.AUTO_INIT_CACHE_BUSTING!==!1&&document.addEventListener("DOMContentLoaded",()=>{let i=new l;i.init({checkInterval:3e4,autoRefresh:!1}),window.addEventListener("cacheVersionUpdated",e=>{console.log("Cache version updated silently:",e.detail)}),window.cacheBustingClient=i});async function f(){if(typeof ej!="undefined"&&ej.base&&ej.base.registerLicense)try{let i=await fetch("/api/syncfusion-license");if(!i.ok){let n=await i.json().catch(()=>({})),a=n.message||n.error||`HTTP ${i.status}`;throw new Error(a)}let t=(await i.json()).license_key;t&&t.trim()!==""?(ej.base.registerLicense(t),console.log("\u2705 Syncfusion license registered successfully")):console.warn("\u26A0\uFE0F Syncfusion license key is empty or invalid")}catch(i){console.error("\u274C Failed to register Syncfusion license:",i.message||i),console.warn("\u26A0\uFE0F Syncfusion will run in trial mode"),console.warn("\u{1F4A1} Please ensure SYNCFUSION_LICENSE_KEY environment variable is set")}else console.warn("\u26A0\uFE0F Syncfusion library not loaded or registerLicense function not available")}f().then(()=>{p()});function p(){fetch("/api/data").then(i=>{if(!i.ok)throw new Error(`Server error: ${i.status}`);return i.json()}).then(i=>{let e=i.map(t=>{var n;return{...t,company:((n=t.company)==null?void 0:n.name)||""}});new ej.grids.Grid({dataSource:e,columns:[{field:"name",headerText:"Name",width:150},{field:"username",headerText:"Username",width:150},{field:"email",headerText:"Email",width:200},{field:"company",headerText:"Company",width:200}],height:400,allowPaging:!0,pageSettings:{pageSize:5}}).appendTo("#Grid")}).catch(i=>{console.error("Error loading data:",i),document.getElementById("Grid").innerHTML='<p style="color:red;">Failed to load data.</p>'})}typeof window!="undefined"&&(window.myAtmApp={version:"1.0.0",initialized:!0});})();
