fetch('/api/data')
    .then(response => {
        if (!response.ok) throw new Error(`Server error: ${response.status}`);
        return response.json();
    })
    .then(data => {
        const flatData = data.map(item => ({
            ...item,
            company: item.company?.name || ''
        }));

        new ej.grids.Grid({
            dataSource: flatData,
            columns: [
                { field: 'name', headerText: 'Name', width: 150 },
                { field: 'username', headerText: 'Username', width: 150 },
                { field: 'email', headerText: 'Email', width: 200 },
                { field: 'company', headerText: 'Company', width: 200 }
            ],
            height: 400,
            allowPaging: true,
            pageSettings: { pageSize: 5 }
        }).appendTo('#Grid');
    })
    .catch(error => {
        console.error('Error loading data:', error);
        document.getElementById('Grid').innerHTML = '<p style="color:red;">Failed to load data.</p>';
    }); 