# Build script for Rust BFF Docker container
Write-Host "🐳 Building Rust BFF Docker container..." -ForegroundColor Cyan

# Build the Docker image
Write-Host "🔨 Building Docker image 'rust-bff-app'..." -ForegroundColor Yellow
docker build -t rust-bff-app .

if ($LASTEXITCODE -eq 0) {
    Write-Host "✅ Docker image built successfully!" -ForegroundColor Green
    Write-Host ""
    Write-Host "To run the container:" -ForegroundColor Cyan
    Write-Host "  docker run -p 8080:8080 rust-bff-app" -ForegroundColor Gray
    Write-Host ""
    Write-Host "Then open: http://localhost:8080" -ForegroundColor Cyan
} else {
    Write-Host "❌ Docker build failed!" -ForegroundColor Red
    exit 1
} 