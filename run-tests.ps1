#!/usr/bin/env pwsh

# run-tests.ps1
# Run tests with code coverage for myatm-embedded-ui backend
# This script uses cargo-tarpaulin for code coverage analysis

param(
    [string]$Format = "html,lcov,stdout",
    [string]$OutputDir = "coverage",
    [switch]$Open = $false,
    [switch]$Verbose = $false,
    [switch]$Clean = $false,
    [string]$FailUnder = "0"
)

# Set error action preference
$ErrorActionPreference = "Stop"

# Colors for output
$Green = "`e[32m"
$Red = "`e[31m"
$Yellow = "`e[33m"
$Blue = "`e[34m"
$Reset = "`e[0m"

function Write-ColorOutput {
    param(
        [Parameter(Mandatory = $true)]
        [string]$Message,
        [string]$Color = $Reset
    )
    Write-Host "${Color}${Message}${Reset}"
}

function Test-CargoTarpaulin {
    try {
        $result = cargo tarpaulin --version 2>$null
        return $true
    }
    catch {
        return $false
    }
}

# Print header
Write-ColorOutput "Running tests with code coverage for myatm-embedded-ui backend" $Blue
Write-ColorOutput "============================================================" $Blue

# Check if we're in the right directory
if (!(Test-Path "backend/Cargo.toml")) {
    Write-ColorOutput "Error: Must run from project root directory (backend/Cargo.toml not found)" $Red
    exit 1
}

# Change to backend directory
Set-Location backend

# Check if cargo-tarpaulin is installed
if (!(Test-CargoTarpaulin)) {
    Write-ColorOutput "Installing cargo-tarpaulin..." $Yellow
    try {
        cargo install cargo-tarpaulin
        Write-ColorOutput "cargo-tarpaulin installed successfully" $Green
    }
    catch {
        Write-ColorOutput "Failed to install cargo-tarpaulin: $_" $Red
        exit 1
    }
}

# Clean previous coverage data if requested
if ($Clean) {
    Write-ColorOutput "Cleaning previous coverage data..." $Yellow
    Remove-Item -Path $OutputDir -Recurse -Force -ErrorAction SilentlyContinue
    cargo clean
}

# Create output directory
if (!(Test-Path $OutputDir)) {
    New-Item -ItemType Directory -Path $OutputDir -Force | Out-Null
}

# Build tarpaulin command
$tarpaulinArgs = @(
    "tarpaulin"
    "--output-dir", $OutputDir
    "--fail-under", $FailUnder
    "--skip-clean"
    "--ignore-tests"
    "--exclude-files", "target/*"
    "--exclude-files", "tests/*"
    "--timeout", "300"
)

# Add output formats
$formats = $Format -split ","
foreach ($fmt in $formats) {
    $tarpaulinArgs += "--out", $fmt.Trim()
}

# Add verbose flag if requested
if ($Verbose) {
    $tarpaulinArgs += "--verbose"
}

# Add engine preference (llvm for Windows, auto for others)
if ($IsWindows) {
    $tarpaulinArgs += "--engine", "llvm"
} else {
    $tarpaulinArgs += "--engine", "auto"
}

Write-ColorOutput "Running cargo tarpaulin..." $Yellow
Write-ColorOutput "Command: cargo $($tarpaulinArgs -join ' ')" $Blue

try {
    # Run tarpaulin
    $startTime = Get-Date
    & cargo @tarpaulinArgs
    $endTime = Get-Date
    $duration = $endTime - $startTime
    
    if ($LASTEXITCODE -ne 0) {
        Write-ColorOutput "Tests failed or coverage is below threshold" $Red
        exit $LASTEXITCODE
    }
    
    Write-ColorOutput "Code coverage analysis completed successfully" $Green
    Write-ColorOutput "Duration: $($duration.TotalSeconds.ToString("F2")) seconds" $Blue
    
    # Display coverage files generated
    Write-ColorOutput "`nCoverage reports generated:" $Blue
    Get-ChildItem -Path $OutputDir -File | ForEach-Object {
        Write-ColorOutput "  - $($_.Name)" $Green
    }
    
    # Display coverage summary if stdout was requested
    if ($Format -match "stdout") {
        Write-ColorOutput "`nCoverage summary displayed above" $Blue
    }
    
    # Open HTML report if requested and available
    if ($Open -and (Test-Path "$OutputDir/tarpaulin-report.html")) {
        Write-ColorOutput "`nOpening HTML coverage report..." $Yellow
        if ($IsWindows) {
            Start-Process "$OutputDir/tarpaulin-report.html"
        } elseif ($IsMacOS) {
            & open "$OutputDir/tarpaulin-report.html"
        } else {
            & xdg-open "$OutputDir/tarpaulin-report.html"
        }
    }
    
    # Display next steps
    Write-ColorOutput "`nNext steps:" $Blue
    Write-ColorOutput "  - View HTML report: $OutputDir/tarpaulin-report.html" $Green
    Write-ColorOutput "  - LCOV file for CI: $OutputDir/lcov.info" $Green
    Write-ColorOutput "  - Upload to codecov.io or coveralls.io using the lcov file" $Green
    
    Write-ColorOutput "`nDone!" $Green
    
}
catch {
    Write-ColorOutput "Error running cargo tarpaulin: $_" $Red
    exit 1
}
finally {
    # Return to original directory
    Set-Location ..
} 