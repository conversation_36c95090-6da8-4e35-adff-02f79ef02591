// # syncfusion-utils.js
//
// Syncfusion utilities for license registration and common functionality
// This module provides reusable Syncfusion-related functions across the application

import { registerLicense } from '@syncfusion/ej2-base';

/**
 * Register Syncfusion license before initializing any components
 * @returns {Promise<void>}
 */
export async function registerSyncfusionLicense() {
    if (registerLicense) {
        try {
            const response = await fetch('/api/syncfusion-license');
            if (!response.ok) {
                const errorData = await response.json().catch(() => ({}));
                const errorMessage = errorData.message || errorData.error || `HTTP ${response.status}`;
                throw new Error(errorMessage);
            }
            
            const data = await response.json();
            const syncfusionLicenseKey = data.license_key;
            
            if (syncfusionLicenseKey && syncfusionLicenseKey.trim() !== '') {
                registerLicense(syncfusionLicenseKey);
                console.log('✅ Syncfusion license registered successfully');
            } else {
                console.warn('⚠️ Syncfusion license key is empty or invalid');
            }
        } catch (error) {
            console.error('❌ Failed to register Syncfusion license:', error.message || error);
            console.warn('⚠️ Syncfusion will run in trial mode');
            console.warn('💡 Please ensure SYNCFUSION_LICENSE_KEY environment variable is set');
        }
    } else {
        console.warn('⚠️ Syncfusion library not loaded or registerLicense function not available');
    }
}

// Make available globally for non-module usage
if (typeof window !== 'undefined') {
    window.syncfusionUtils = {
        registerSyncfusionLicense
    };
}