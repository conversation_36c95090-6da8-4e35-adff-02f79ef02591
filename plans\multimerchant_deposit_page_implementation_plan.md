# ✅✅ Implementation Plan – Multi-Merchant Deposit Page ✅✅

*This plan has been fully implemented and all steps are complete.*

**Overview**

* We will enhance the `/deposits` page.
* A new “multi-merchant” mode must be toggled by the `multiMerchant=true` query parameter.
* The BFF code will call a different backend API method but use the same parameters.
* In multi-merchant mode, the grid will show these columns:
  Deposit Date | Terminal ID | *Sub Terminal ID* | Depositor Name | Trace No | *Business Entity ID* | Deposited | Settled

---

## 1 Prepare workspace

1. Load the codebase into the agent’s work folder
2. Install dependencies (`npm install`, `cargo build` etc)
3. Run existing tests to confirm no failures

## 2 Front-end: read flags

In `src/frontend/deposits.js` inside `init()` add:

```js
const params        = new URLSearchParams(window.location.search)
this.terminalId    = params.get('terminalId') || ''
this.multiMerchant = params.get('multiMerchant') === 'true'

```

## 3 Front-end: build data source URL

Replace the existing URL builder in `initializeGrid()` with:

```js
const parts = []
if (this.terminalId)    parts.push(`terminalId=${encodeURIComponent(this.terminalId)}`)
if (this.multiMerchant) parts.push('multiMerchant=true')
const qs            = parts.length ? '?' + parts.join('&') : ''
const dataSourceUrl = '/api/deposits' + qs
this.grid.dataSource = new DataManager({
  url    : dataSourceUrl,
  adaptor: new ODataV4Adaptor()
})
```

## 4 Front-end: add new columns

In the same `initializeGrid()` update the `columns` array. After existing columns insert:

```js
...(this.multiMerchant ? [
  { field: 'DepositDateTime',    headerText: 'Deposit Date',      textAlign: 'Centre', width: 180 },
  { field: 'TerminalId',         headerText: 'Terminal ID',       textAlign: 'Centre', width: 120 },
  { field: 'EntityTerminalId',   headerText: 'Sub Terminal ID',   textAlign: 'Centre', width: 150 },
  { field: 'DepositorName',      headerText: 'Depositor Name',    textAlign: 'Left',   width: 200 },
  { field: 'TraceNr',            headerText: 'Trace No',          textAlign: 'Centre', width: 120 },
  { field: 'EntityName',         headerText: 'Business Entity ID',textAlign: 'Left',   width: 200 },
  { field: 'CashDeposited',      headerText: 'Deposited',         textAlign: 'Right',  width: 120 },
  { field: 'SettledAmount',      headerText: 'Settled',           textAlign: 'Right',  width: 120 }
] : [])
```

## 5 Back-end: choose endpoint

In `backend/src/deposits/handlers.rs` locate the code that builds `external_url` and replace with:

```rust
let multi = params.get("multiMerchant").map(|v| v == "true").unwrap_or(false)
let endpoint = if multi {
  "externalDsMultiMerchantDeposits"
} else {
  "externalDsMerchantDepositAggregates"
}
let external_url = format!("{}/odata/{}", base_url, endpoint)
```

## 6 Back-end: forward full query

Ensure all query params go through, for example:

```rust
let qs = params.iter()
  .map(|(k, v)| format!("{}={}", k, v))
  .collect::<Vec<_>>()
  .join("&");
let full_url = format!("{}?{}", external_url, qs);
// then issue GET to full_url
```

## 7 Tests – back end

In `backend/tests/deposits_handlers_tests.rs` add tests that:

* With `multiMerchant=true` the URL contains `/externalDsMultiMerchantDeposits`
* With other combos it uses `/odata/externalDsMerchantDepositAggregates`

## 8 Tests – front end

In `src/frontend/__tests__/deposits.test.js` add a test that:

* Loads `/deposits?terminalId=X&multiMerchant=true` and asserts the grid shows the eight new columns
* Loads without flags and asserts they are absent

## 9 Documentation

In `README.md` under the `/deposits` section:

* Note the new flags `multiMerchant` 
* Show example URL for multi-merchant mode
* Describe the extra fields

## 10 Commit and PR

1. Stage front-end and back-end changes
2. Commit with message


   ```
   feat(deposits): support multi-merchant flag, switch endpoint, add entity columns
   ```
3. Open a pull request and describe:

   * The new flags and how they work
   * Endpoint selection logic
   * New columns and how to verify them

## 11 Supporting information

* The query parameters (`terminalId`, `multiMerchant`) stay the same for both modes.

* Only the OData route changes depending on flags:

  * Default route:

    ```
    /odata/externalDsMerchantDepositAggregates
    ```
  * Multi-merchant mode route:

    ```
    /odata/externalDsMultiMerchantDeposits
    ```

* Sample OData response from the multi-merchant endpoint:

  ```json
  {
      "@odata.context": "http://merchant-deposits-kube-ds.paycorp.co.za/odata/$metadata#ExternalDsMultiMerchantDeposits",
      "@odata.count": 597,
      "value": [
          {
              "RecordId": 3572375,
              "RecordGuid": "527ead3e-3c6e-4822-9e20-b7c0c304e8b2",
              "SubmitSettlementDate": "2025-07-10T00:00:00Z",
              "DepositDateTime": "2025-07-10T10:45:47Z",
              "TerminalId": "ATMH0582",
              "DepositorName": "Mamabolo Herbet PNP",
              "DepositorCode": "05825001",
              "EntityTerminalId": "ATMH0582A",
              "EntityName": "BP Empire NAMOS",
              "CashDeposited": 4000,
              "SettledAmount": 0,
              "TotalUnsettled": -4000,
              "TraceNr": "482166"
          },
          {
              "RecordId": 3572351,
              "RecordGuid": "527ead3e-3c6e-4822-9e20-b7c0c304e8b2",
              "SubmitSettlementDate": "2025-07-10T00:00:00Z",
              "DepositDateTime": "2025-07-10T10:43:11Z",
              "TerminalId": "ATMH0582",
              "DepositorName": "Mamabolo Herbet PNP",
              "DepositorCode": "05825001",
              "EntityTerminalId": "ATMH0582A",
              "EntityName": "BP Empire NAMOS",
              "CashDeposited": 9650,
              "SettledAmount": 0,
              "TotalUnsettled": -9650,
              "TraceNr": "481859"
          }
      ]
  }
  ```
