Here is the complete, ready-to-execute **implementation plan** for building a **Rust BFF (Backend-for-Frontend)** with a **Vanilla JS + Syncfusion** frontend, packaged into a **single Docker container** using a `scratch` base image.

---

## ✅ Full Implementation Plan: Rust BFF + Vanilla JS + Syncfusion Grid

### 🔧 Purpose

Build a lightweight web app that:

* Serves static frontend files (vanilla JS + Syncfusion)
* Proxies requests to a public third-party API via Rust backend
* Hides API interaction from the frontend (for security/flexibility)
* Runs in a single, minimal Docker container with no shell or external runtime

---

## 📁 Project Structure

```
sample-bff-app/
├── backend/
│   ├── Cargo.toml
│   └── src/
│       └── main.rs
├── frontend/
│   ├── index.html
│   └── app.js
├── Dockerfile
```

---

## 🧱 1. Frontend

### `frontend/index.html`

```html
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8" />
    <title>User Data Grid</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <link href="https://cdn.syncfusion.com/ej2/23.2.4/material.css" rel="stylesheet" />
    <script src="https://cdn.syncfusion.com/ej2/23.2.4/dist/ej2.min.js"></script>
    <style>
        body {
            font-family: sans-serif;
            margin: 20px;
        }
        #Grid {
            max-width: 900px;
            margin: auto;
        }
    </style>
</head>
<body>
    <h2>User Directory</h2>
    <div id="Grid"></div>
    <script src="app.js"></script>
</body>
</html>
```

---

### `frontend/app.js`

```javascript
fetch('/api/data')
    .then(response => {
        if (!response.ok) throw new Error(`Server error: ${response.status}`);
        return response.json();
    })
    .then(data => {
        const flatData = data.map(item => ({
            ...item,
            company: item.company?.name || ''
        }));

        new ej.grids.Grid({
            dataSource: flatData,
            columns: [
                { field: 'name', headerText: 'Name', width: 150 },
                { field: 'username', headerText: 'Username', width: 150 },
                { field: 'email', headerText: 'Email', width: 200 },
                { field: 'company', headerText: 'Company', width: 200 }
            ],
            height: 400,
            allowPaging: true,
            pageSettings: { pageSize: 5 }
        }).appendTo('#Grid');
    })
    .catch(error => {
        console.error('Error loading data:', error);
        document.getElementById('Grid').innerHTML = '<p style="color:red;">Failed to load data.</p>';
    });
```

---

## 🦀 2. Backend (Rust + Axum)

### `backend/Cargo.toml`

```toml
[package]
name = "bff_app"
version = "0.1.0"
edition = "2021"

[dependencies]
axum = "0.7"
tokio = { version = "1", features = ["full"] }
tower-http = { version = "0.5", features = ["serve-dir"] }
reqwest = { version = "0.12", features = ["json", "rustls-tls"] }
serde = { version = "1", features = ["derive"] }
serde_json = "1"
tracing-subscriber = "0.3"
```

---

### `backend/src/main.rs`

```rust
use axum::{
    response::{IntoResponse, Response},
    routing::get,
    Json, Router,
};
use serde_json::Value;
use std::{net::SocketAddr};
use tower_http::services::ServeDir;

#[tokio::main]
async fn main() {
    let app = Router::new()
        .route("/api/data", get(proxy_users))
        .nest_service("/", ServeDir::new("./static"));

    let addr = SocketAddr::from(([0, 0, 0, 0], 8080));
    println!("Running on http://{addr}");

    axum::Server::bind(&addr)
        .serve(app.into_make_service())
        .await
        .unwrap();
}

async fn proxy_users() -> Response {
    match reqwest::get("https://jsonplaceholder.typicode.com/users").await {
        Ok(resp) => match resp.json::<Value>().await {
            Ok(json) => Json(json).into_response(),
            Err(_) => (axum::http::StatusCode::BAD_GATEWAY, "Invalid JSON").into_response(),
        },
        Err(_) => (axum::http::StatusCode::BAD_GATEWAY, "API unreachable").into_response(),
    }
}
```

---

## 🐳 3. Dockerfile (Multi-stage, ends in `scratch`)

```Dockerfile
# Stage 1: Build Rust binary
FROM rust:1.79-alpine AS builder
RUN apk add --no-cache musl-dev
WORKDIR /app
COPY backend/Cargo.toml backend/Cargo.lock ./
RUN mkdir src && echo "fn main() {}" > src/main.rs
RUN rustup target add x86_64-unknown-linux-musl
RUN cargo fetch
COPY backend/ .
RUN cargo build --release --target x86_64-unknown-linux-musl

# Stage 2: Final scratch image
FROM scratch
WORKDIR /app
COPY --from=builder /app/target/x86_64-unknown-linux-musl/release/bff_app .
COPY frontend ./static
CMD ["./bff_app"]
```

---

## ▶️ 4. Build and Run

```bash
docker build -t rust-bff-app .
docker run -p 8080:8080 rust-bff-app
```

Then open [http://localhost:8080](http://localhost:8080) in a browser.

---

## ✅ What This Demonstrates

* Serving a fully static frontend (no framework or Node.js)
* Using **Rust as a secure BFF**, hiding all API interactions
* Packaging everything in a single, low-footprint image
* Clean separation of concerns: frontend stays simple, backend handles API calls

