// cache_middleware.rs

//! Cache Busting Middleware and State Management
//! 
//! This module provides middleware and state management for cache busting functionality
//! in the ATM embedded UI application. It works in conjunction with the static file
//! server to ensure proper cache invalidation and optimal asset delivery.
//! 
//! ## Core Functionality:
//! 
//! ### 1. Cache Busting State Management
//! - Maintains application-wide cache version information
//! - Tracks asset metadata including URLs, versions, sizes, and content types
//! - Provides thread-safe access to asset information via Arc<RwLock>
//! - Automatic version generation using timestamps when no version provided
//! 
//! ### 2. Asset Information Tracking
//! - Stores comprehensive metadata for each asset (name, URL, version, size, etc.)
//! - Provides asset manifest generation for client-side cache management
//! - Tracks last modification times for proper cache validation
//! - Supports dynamic asset registration and updates
//! 
//! ### 3. HTTP Cache Header Management
//! - Generates appropriate cache control headers based on asset type and versioning
//! - Implements different caching strategies:
//!   * Versioned assets: 30 days immutable caching
//!   * HTML files: No caching with must-revalidate
//!   * Other assets: 1 day default caching
//! - Proper content-type header generation
//! 
//! ### 4. Version Validation and Query Processing
//! - Extracts and validates version parameters from query strings
//! - Compares client-side versions with current server version
//! - Supports cache invalidation based on version mismatches
//! 
//! ## Data Structures:
//! 
//! - **CacheBustingState**: Main application state holding version and asset information
//! - **AssetInfo**: Detailed metadata for individual assets
//! - **AssetQuery**: Query parameter parsing for version information
//! - **VersionResponse**: API response format for version endpoint
//! - **AssetManifest**: Client-consumable asset manifest format
//! 
//! ## Important Notes:
//! 
//! - **Thread Safety**: Uses Arc<RwLock> for concurrent access to asset information
//! - **Performance**: Optimized for read-heavy workloads with RwLock
//! - **Flexibility**: Supports both automatic timestamp-based and custom versioning
//! - **Integration**: Designed to work seamlessly with static_files.rs module
//! - **Production Ready**: Implements proper HTTP caching semantics
//! 
//! ## Usage:
//! 
//! Initialize cache busting state:
//! ```rust
//! let cache_state = CacheBustingState::new(Some("1.2.3".to_string()));
//! ```
//! 
//! Add asset information:
//! ```rust
//! cache_state.add_asset("app.js".to_string(), AssetInfo {
//!     name: "app.js".to_string(),
//!     url: "app.js?v=1.2.3".to_string(),
//!     version: "1.2.3".to_string(),
//!     size: 12345,
//!     content_type: "application/javascript".to_string(),
//!     last_modified: timestamp,
//! });
//! ```

use axum::{
    extract::Query,
    http::{header, HeaderMap, HeaderValue},
};
use serde::{Deserialize, Serialize};
use std::{
    collections::HashMap,
    sync::{Arc, RwLock},
    time::{SystemTime, UNIX_EPOCH},
};

/// Application state for cache busting
#[derive(Debug, Clone)]
pub struct CacheBustingState {
    pub version: String,
    pub assets: Arc<RwLock<HashMap<String, AssetInfo>>>,
}

/// Asset information for cache busting
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AssetInfo {
    pub name: String,
    pub url: String,
    pub version: String,
    pub size: u64,
    pub content_type: String,
    pub last_modified: u64,
}

/// Query parameters for asset requests
#[allow(dead_code)]
#[derive(Debug, Deserialize)]
pub struct AssetQuery {
    pub v: Option<String>,
}

/// Response for version endpoint
#[derive(Debug, Serialize)]
pub struct VersionResponse {
    pub version: String,
    pub timestamp: u64,
    pub assets: HashMap<String, AssetInfo>,
}

/// Response for asset manifest endpoint
#[derive(Debug, Serialize)]
pub struct AssetManifest {
    pub version: String,
    pub assets: HashMap<String, String>,
}

impl CacheBustingState {
    pub fn new(version: Option<String>) -> Self {
        let version = version.unwrap_or_else(|| {
            SystemTime::now()
                .duration_since(UNIX_EPOCH)
                .unwrap()
                .as_secs()
                .to_string()
        });

        Self {
            version,
            assets: Arc::new(RwLock::new(HashMap::new())),
        }
    }

    #[allow(dead_code)]
    pub fn add_asset(&self, name: String, asset_info: AssetInfo) {
        if let Ok(mut assets) = self.assets.write() {
            assets.insert(name, asset_info);
        }
    }

    #[allow(dead_code)]
    pub fn get_asset_url(&self, name: &str) -> Option<String> {
        if let Ok(assets) = self.assets.read() {
            assets.get(name).map(|info| info.url.clone())
        } else {
            None
        }
    }
}











/// Helper function to extract version from query params
#[allow(dead_code)]
pub fn get_version_from_query(query: &Query<AssetQuery>) -> Option<&str> {
    query.v.as_deref()
}

/// Helper function to validate cache version
#[allow(dead_code)]
pub fn is_valid_cache_version(version: &str, current_version: &str) -> bool {
    version == current_version
}

/// Helper function to generate cache headers
#[allow(dead_code)]
pub fn generate_cache_headers(is_versioned: bool, content_type: &str) -> HeaderMap {
    let mut headers = HeaderMap::new();
    
    headers.insert(
        header::CONTENT_TYPE,
        HeaderValue::from_str(content_type).unwrap_or_else(|_| HeaderValue::from_static("application/octet-stream")),
    );

    let cache_control = if is_versioned {
        "public, max-age=2592000, immutable" // 30 days for versioned assets
    } else if content_type.starts_with("text/html") {
        "public, max-age=0, must-revalidate" // No caching for HTML
    } else {
        "public, max-age=86400" // 1 day for other assets
    };

    headers.insert(
        header::CACHE_CONTROL,
        HeaderValue::from_static(cache_control),
    );

    headers
} 