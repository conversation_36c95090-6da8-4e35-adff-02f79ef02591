// # deposits/handlers.rs
//
// Deposits API Proxy Handlers
//
// This module provides HTTP handlers for proxying deposit-related requests to external APIs.
// It serves as a Backend-for-Frontend (BFF) layer that handles authentication, request routing,
// and response forwarding for the deposits functionality.
//
// ## Key Features
// - **Authentication Proxy**: Generates JWT tokens for external API authentication
// - **Request Forwarding**: Transparently forwards OData v4 queries to external deposits API
// - **Query Parameter Processing**: Parses and logs OData filter expressions for monitoring
// - **Error Handling**: Provides robust error handling with appropriate HTTP status codes
// - **Health Monitoring**: Includes health check endpoint for service monitoring
// - **Timeout Management**: Configurable request timeouts for external API calls
//
// ## Supported Endpoints
// - `GET /api/deposits` - Proxy to external deposits API with OData v4 support
// - `GET /api/deposits/health` - Health check for deposits functionality
//
// ## Authentication Flow
// The proxy generates JWT tokens dynamically for each request to the external API,
// eliminating the need for storing static bearer tokens.
//
// ## Configuration
// Uses `DepositsConfig` for external API URL and timeout settings.
//
// ## Error Handling
// Returns appropriate HTTP status codes:
// - 400 (Bad Request): Invalid query parameters
// - 500 (Internal Server Error): JWT generation or response building failures
// - 502 (Bad Gateway): External API communication errors

use axum::{
    extract::{Query, Request, State},
    http::{HeaderMap, StatusCode},
    response::Response,
    Json,
};
use serde_json::Value;
use std::collections::HashMap;
use std::time::Duration;
use tracing::{debug, error, info, instrument};

use crate::config::AppConfig;
use crate::deposits::odata::parse_odata_filter;

/// Proxy handler for deposits endpoint
/// Forwards OData v4 queries to the external deposits API
#[instrument(name = "deposits_proxy", skip_all)]
pub async fn deposits_proxy(
    State(state): State<AppConfig>,
    Query(params): Query<HashMap<String, String>>,
    request: Request,
) -> Result<Response, StatusCode> {
    info!("Proxying request to deposits API");

    // Log the incoming query parameters for debugging
    debug!("Query params: {:?}", params);

    // Parse OData filter for logging if present
    if let Some(filter) = params.get("$filter") {
        let (terminal_ids, start_date, end_date) = parse_odata_filter(filter);
        debug!(
            "Parsed filter - Terminal IDs: {:?}, Start: {:?}, End: {:?}",
            terminal_ids, start_date, end_date
        );
    }

    // Build the external API URL using deposits config
    let base_url = &state.deposits.external_api.base_url;
    let multi = params.get("multiMerchant").map(|v| v == "true").unwrap_or(false);
    let mca = params.get("mca").map(|v| v == "true").unwrap_or(false);
    
    let endpoint = if multi {
        "externalDsMultiMerchantDeposits"
    } else if mca {
        "externalDsMcaMerchantDeposits"
    } else {
        "externalDsMerchantDepositAggregates"
    };
    let external_url = format!("{}/odata/{}", base_url, endpoint);
    // Forward all query params
    let full_url = if !params.is_empty() {
        let qs = params.iter().map(|(k, v)| format!("{}={}", k, v)).collect::<Vec<_>>().join("&");
        format!("{}?{}", external_url, qs)
    } else {
        external_url
    };
    debug!("Forwarding request to: {}", full_url);

    // Create HTTP client with timeout
    let client = reqwest::Client::builder()
        .timeout(Duration::from_secs(state.deposits.external_api.timeout_seconds))
        .build()
        .map_err(|e| {
            error!("Failed to create HTTP client: {}", e);
            StatusCode::INTERNAL_SERVER_ERROR
        })?;

    // Prepare request headers
    let mut headers = HeaderMap::new();
    
    // Generate JWT token for authentication instead of using static bearer token
    match crate::auth::generate_jwt_token() {
        Ok(jwt_token) => {
            headers.insert(
                "Authorization",
                format!("Bearer {}", jwt_token).parse().map_err(|e| {
                    error!("Failed to create authorization header: {}", e);
                    StatusCode::INTERNAL_SERVER_ERROR
                })?,
            );
            debug!("Generated JWT token for external API authentication");
        }
        Err(e) => {
            error!("Failed to generate JWT token: {}", e);
            return Err(StatusCode::INTERNAL_SERVER_ERROR);
        }
    }

    // Forward Content-Type if present
    if let Some(content_type) = request.headers().get("content-type") {
        headers.insert("content-type", content_type.clone());
    }

    // Forward Accept header if present
    if let Some(accept) = request.headers().get("accept") {
        headers.insert("accept", accept.clone());
    }

    // Make the request to external API
    let response = client
        .get(&full_url)
        .headers(headers)
        .send()
        .await
        .map_err(|e| {
            error!("Failed to send request to external API: {}", e);
            StatusCode::BAD_GATEWAY
        })?;

    let status = response.status();
    let headers = response.headers().clone();

    // Read response body
    let body = response.text().await.map_err(|e| {
        error!("Failed to read response body: {}", e);
        StatusCode::BAD_GATEWAY
    })?;

    debug!("External API response status: {}", status);
    debug!("External API response body length: {}", body.len());

    // Create response
    let mut response_builder = Response::builder().status(status);

    // Forward relevant headers
    for (key, value) in headers.iter() {
        let key_str = key.as_str();
        if key_str.starts_with("content-") || key_str == "cache-control" {
            response_builder = response_builder.header(key, value);
        }
    }

    // Build and return response
    if multi {
        // For multiMerchant, deserialize and re-serialize to apply custom serializers
        let parsed: Result<crate::models::ODataResponse<crate::models::ExternalDsMultiMerchantDeposit>, _> = serde_json::from_str(&body);
        match parsed {
            Ok(model) => {
                let json = serde_json::to_string(&model).map_err(|e| {
                    error!("Failed to serialize model: {}", e);
                    StatusCode::INTERNAL_SERVER_ERROR
                })?;
                let response = response_builder
                    .body(json.into())
                    .map_err(|e| {
                        error!("Failed to build response: {}", e);
                        StatusCode::INTERNAL_SERVER_ERROR
                    })?;
                info!("Successfully proxied and transformed multiMerchant deposits request");
                Ok(response)
            }
            Err(e) => {
                error!("Failed to parse external API response: {}", e);
                // Fallback: return the original body
                let response = response_builder
                    .body(body.into())
                    .map_err(|e| {
                        error!("Failed to build response: {}", e);
                        StatusCode::INTERNAL_SERVER_ERROR
                    })?;
                Ok(response)
            }
        }
    } else if mca {
        // For MCA, deserialize and re-serialize to apply custom serializers
        let parsed: Result<crate::models::ODataResponse<crate::models::ExternalDsMcaMerchantDeposit>, _> = serde_json::from_str(&body);
        match parsed {
            Ok(model) => {
                let json = serde_json::to_string(&model).map_err(|e| {
                    error!("Failed to serialize model: {}", e);
                    StatusCode::INTERNAL_SERVER_ERROR
                })?;
                let response = response_builder
                    .body(json.into())
                    .map_err(|e| {
                        error!("Failed to build response: {}", e);
                        StatusCode::INTERNAL_SERVER_ERROR
                    })?;
                info!("Successfully proxied and transformed MCA deposits request");
                Ok(response)
            }
            Err(e) => {
                error!("Failed to parse external API response: {}", e);
                // Fallback: return the original body
                let response = response_builder
                    .body(body.into())
                    .map_err(|e| {
                        error!("Failed to build response: {}", e);
                        StatusCode::INTERNAL_SERVER_ERROR
                    })?;
                Ok(response)
            }
        }
    } else {
        // For merchant aggregates, deserialize and re-serialize to apply custom serializers
        let parsed: Result<crate::models::ODataResponse<crate::models::MerchantDeposit>, _> = serde_json::from_str(&body);
        match parsed {
            Ok(model) => {
                let json = serde_json::to_string(&model).map_err(|e| {
                    error!("Failed to serialize model: {}", e);
                    StatusCode::INTERNAL_SERVER_ERROR
                })?;
                let response = response_builder
                    .body(json.into())
                    .map_err(|e| {
                        error!("Failed to build response: {}", e);
                        StatusCode::INTERNAL_SERVER_ERROR
                    })?;
                info!("Successfully proxied and transformed merchant deposits request");
                Ok(response)
            }
            Err(e) => {
                error!("Failed to parse external API response: {}", e);
                // Fallback: return the original body
                let response = response_builder
                    .body(body.into())
                    .map_err(|e| {
                        error!("Failed to build response: {}", e);
                        StatusCode::INTERNAL_SERVER_ERROR
                    })?;
                Ok(response)
            }
        }
    }
}

/// Health check for deposits functionality
#[instrument(name = "deposits_health")]
pub async fn deposits_health(State(state): State<AppConfig>) -> Result<Json<Value>, StatusCode> {
    // Test JWT token generation
    let jwt_generation_status = match crate::auth::generate_jwt_token() {
        Ok(_) => "working",
        Err(_) => "failed"
    };
    
    let health_data = serde_json::json!({
        "status": "healthy",
        "deposits_external_api_base_url": state.deposits.external_api.base_url,
        "deposits_external_api_timeout": state.deposits.external_api.timeout_seconds,
        "jwt_token_generation": jwt_generation_status,
        "auth_method": "JWT_generation"
    });

    Ok(Json(health_data))
}

 