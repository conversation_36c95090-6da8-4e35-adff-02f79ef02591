#!/usr/bin/env pwsh
# Test script specifically for compression verification

Write-Host "🗜️  Testing Axum Compression Features" -ForegroundColor Green
Write-Host "=====================================" -ForegroundColor Green

# Check if curl is available
if (-not (Get-Command curl -ErrorAction SilentlyContinue)) {
    Write-Host "❌ curl not found. Please install curl or use Windows with curl available." -ForegroundColor Red
    exit 1
}

# Start the server
Write-Host "Starting server..." -ForegroundColor Yellow
$serverProcess = Start-Process -FilePath ".\backend\target\release\myatm-embedded-ui.exe" -PassThru -NoNewWindow

# Wait for server to start
Start-Sleep -Seconds 3

Write-Host "Server started. Running compression tests..." -ForegroundColor Yellow

try {
    # Test 1: No compression headers
    Write-Host "`n🔍 Test 1: Request without compression headers" -ForegroundColor Cyan
    $result1 = curl -s -w "%{size_download}|%{content_type}|%{http_code}" -o nul http://localhost:8000/api/performance-test
    Write-Host "Response: $result1" -ForegroundColor Green
    
    # Test 2: With gzip compression
    Write-Host "`n🗜️  Test 2: Request with gzip compression" -ForegroundColor Cyan
    $headers = curl -s -H "Accept-Encoding: gzip" -I http://localhost:8000/api/performance-test
    Write-Host "Headers with gzip:" -ForegroundColor Green
    Write-Host $headers -ForegroundColor White
    
    # Test 3: With brotli compression
    Write-Host "`n🗜️  Test 3: Request with brotli compression" -ForegroundColor Cyan
    $headers2 = curl -s -H "Accept-Encoding: br" -I http://localhost:8000/api/performance-test
    Write-Host "Headers with brotli:" -ForegroundColor Green
    Write-Host $headers2 -ForegroundColor White
    
    # Test 4: Size comparison
    Write-Host "`n📊 Test 4: Size comparison" -ForegroundColor Cyan
    $uncompressed = curl -s http://localhost:8000/api/performance-test | Measure-Object -Character | Select-Object -ExpandProperty Characters
    $compressed = curl -s -H "Accept-Encoding: gzip" http://localhost:8000/api/performance-test | Measure-Object -Character | Select-Object -ExpandProperty Characters
    
    Write-Host "Uncompressed size: $uncompressed characters" -ForegroundColor Green
    Write-Host "Compressed size: $compressed characters" -ForegroundColor Green
    
    if ($compressed -lt $uncompressed) {
        Write-Host "✅ Compression is working! Reduced by $($uncompressed - $compressed) characters" -ForegroundColor Green
    } else {
        Write-Host "⚠️  Compression may not be working as expected" -ForegroundColor Yellow
    }
    
    # Test 5: Security headers
    Write-Host "`n🔒 Test 5: Security headers verification" -ForegroundColor Cyan
    $securityHeaders = curl -s -I http://localhost:8000/health/live
    Write-Host "Security headers:" -ForegroundColor Green
    Write-Host $securityHeaders -ForegroundColor White
    
    Write-Host "`n✅ Compression tests completed!" -ForegroundColor Green
    
} catch {
    Write-Host "❌ Error during testing: $_" -ForegroundColor Red
} finally {
    # Clean up
    Write-Host "`n🧹 Stopping server..." -ForegroundColor Yellow
    if ($serverProcess -and !$serverProcess.HasExited) {
        $serverProcess.Kill()
        $serverProcess.WaitForExit()
    }
    Write-Host "Server stopped." -ForegroundColor Green
}

Write-Host "`n🏁 Compression test completed!" -ForegroundColor Green 