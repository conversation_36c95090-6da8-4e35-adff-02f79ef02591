<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8" />
    <title>myDeposits</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta name="description" content="myDeposits - Manage your deposits efficiently with a user-friendly data grid interface." />
    <!-- CSP is now set via HTTP headers from the backend -->
    <link rel="icon" type="image/svg+xml" href="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32'%3E%3Crect x='2' y='2' width='10' height='10' fill='%23007bff'/%3E%3Crect x='12' y='2' width='10' height='10' fill='%23007bff'/%3E%3Crect x='22' y='2' width='8' height='10' fill='%23007bff'/%3E%3Crect x='2' y='12' width='10' height='10' fill='%23007bff'/%3E%3Crect x='12' y='12' width='10' height='10' fill='%23007bff'/%3E%3Crect x='22' y='12' width='8' height='10' fill='%23007bff'/%3E%3Crect x='2' y='22' width='10' height='8' fill='%23007bff'/%3E%3Crect x='12' y='22' width='10' height='8' fill='%23007bff'/%3E%3Crect x='22' y='22' width='8' height='8' fill='%23007bff'/%3E%3C/svg%3E" />
    <link href="themes/material/material.min.css" rel="stylesheet" />
    <script src="https://cdn.syncfusion.com/ej2/30.1.38/dist/ej2.min.js"></script>
    
    
    
    <!-- Bundled CSS will be injected here by the build process -->
    <link rel="stylesheet" href="main.css?v=20250716143242">
</head>
<body>
    <!-- Cache update notification (hidden by default) -->
    <div id="cache-notification" class="cache-notification">
        <div id="notification-message">Cache updated successfully!</div>
    </div>

    <div class="dev-controls" data-production="false">
        <div>Development Controls</div>
        <button onclick="refreshCache()">Refresh Cache</button>
        <button onclick="checkForUpdates()">Check Updates</button>
        <button onclick="showCacheInfo()">Show Cache Info</button>
    </div>

    <div id="main-content">
        <!-- Dynamic content will be loaded here -->
        <div class="loading-initial">
            <h2>Loading...</h2>
            <div class="spinner"></div>
        </div>
    </div>

    <!-- Enhanced cache busting integration -->
    <script>
        // Development helper functions (only show notifications when manually triggered)
        async function refreshCache() {
            if (window.cacheBustingClient) {
                try {
                    showCacheNotification('🔄 Refreshing cache...', 'updating', 2000);
                    await window.cacheBustingClient.refreshCache();
                    showCacheNotification('✅ Cache refreshed successfully!', 'success', 3000);
                } catch (error) {
                    console.error('Failed to refresh cache:', error);
                    showCacheNotification('❌ Failed to refresh cache', 'error', 3000);
                }
            }
        }

        async function checkForUpdates() {
            if (window.cacheBustingClient) {
                try {
                    showCacheNotification('🔍 Checking for updates...', 'updating', 2000);
                    await window.cacheBustingClient.checkForUpdates();
                    showCacheNotification('✅ Check complete', 'success', 2000);
                } catch (error) {
                    console.error('Failed to check for updates:', error);
                    showCacheNotification('❌ Update check failed', 'error', 3000);
                }
            }
        }

        async function showCacheInfo() {
            if (window.cacheBustingClient) {
                const version = window.cacheBustingClient.getCurrentVersion();
                const manifest = window.cacheBustingClient.getAssetManifest();
                
                console.log('Cache Information:', {
                    version,
                    manifest,
                    timestamp: new Date().toISOString()
                });
                
                alert(`Cache Version: ${version}\nAssets: ${Object.keys(manifest || {}).length}\nCheck console for details.`);
            }
        }

        // Show cache notification
        function showCacheNotification(message, type = 'success', duration = 4000) {
            const notification = document.getElementById('cache-notification');
            const messageElement = document.getElementById('notification-message');
            
            messageElement.textContent = message;
            
            // Reset classes
            notification.className = 'cache-notification';
            
            // Add type class
            if (type === 'updating') {
                notification.classList.add('updating');
            } else if (type === 'error') {
                notification.classList.add('error');
            }
            
            // Show notification
            notification.classList.add('show');
            
            // Auto-hide after duration
            setTimeout(() => {
                notification.classList.remove('show');
            }, duration);
        }

        // Listen for cache version updates (silent logging only)
        window.addEventListener('cacheVersionUpdated', (event) => {
            console.log('Cache version updated:', event.detail);
            // Silent cache updating - no user notification
        });

        // Initialize cache system silently
        document.addEventListener('DOMContentLoaded', () => {
            // Cache system initializes silently in the background
            if (window.cacheBustingClient) {
                console.log('Cache busting system initialized');
            }
        });
    </script>
    
    <!-- Bundled JavaScript will be injected here by the build process -->
    <script src="main.js?v=20250716143242"></script>
</body>
</html> 