# Simple script to update cache version for JavaScript files
param(
    [string]$Version = $null
)

if ([string]::IsNullOrEmpty($Version)) {
    $Version = Get-Date -Format "yyyyMMddHHmmss"
}

Write-Host "Updating cache version to: $Version" -ForegroundColor Cyan

# Update backend/static/index.html
if (Test-Path "backend/static/index.html") {
    $content = Get-Content "backend/static/index.html" -Raw
    $content = $content -replace '(\.js)\?v=[^"]*', "`$1?v=$Version"
    $content = $content -replace '(\.js)(?=["''])', "`$1?v=$Version"
    Set-Content "backend/static/index.html" -Value $content
    Write-Host "Updated backend/static/index.html" -ForegroundColor Green
}

# Update frontend/index.html
if (Test-Path "frontend/index.html") {
    $content = Get-Content "frontend/index.html" -Raw
    $content = $content -replace '(\.js)\?v=[^"]*', "`$1?v=$Version"
    $content = $content -replace '(\.js)(?=["''])', "`$1?v=$Version"
    Set-Content "frontend/index.html" -Value $content
    Write-Host "Updated frontend/index.html" -ForegroundColor Green
}

Write-Host "Cache version updated successfully!" -ForegroundColor Green 