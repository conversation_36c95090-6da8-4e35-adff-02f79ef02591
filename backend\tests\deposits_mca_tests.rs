// # deposits_mca_tests.rs
//
// Tests for MCA (Merchant Cash-Advance) mode functionality

use std::collections::HashMap;

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_mca_endpoint_selection() {
        // Test that mca=true selects the MCA endpoint
        let mut params = HashMap::new();
        params.insert("mca".to_string(), "true".to_string());
        
        let multi = params.get("multiMerchant").map(|v| v == "true").unwrap_or(false);
        let mca = params.get("mca").map(|v| v == "true").unwrap_or(false);
        
        let endpoint = if multi {
            "externalDsMultiMerchantDeposits"
        } else if mca {
            "externalDsMcaMerchantDeposits"
        } else {
            "externalDsMerchantDepositAggregates"
        };
        
        assert_eq!(endpoint, "externalDsMcaMerchantDeposits");
        assert!(!multi);
        assert!(mca);
    }

    #[test]
    fn test_multimerchant_wins_over_mca() {
        // Test that multiMerchant=true wins when both mca=true and multiMerchant=true are present
        let mut params = HashMap::new();
        params.insert("mca".to_string(), "true".to_string());
        params.insert("multiMerchant".to_string(), "true".to_string());
        
        let multi = params.get("multiMerchant").map(|v| v == "true").unwrap_or(false);
        let mca = params.get("mca").map(|v| v == "true").unwrap_or(false);
        
        let endpoint = if multi {
            "externalDsMultiMerchantDeposits"
        } else if mca {
            "externalDsMcaMerchantDeposits"
        } else {
            "externalDsMerchantDepositAggregates"
        };
        
        assert_eq!(endpoint, "externalDsMultiMerchantDeposits");
        assert!(multi);
        assert!(mca);
    }

    #[test]
    fn test_default_endpoint_when_no_flags() {
        // Test that default endpoint is used when no flags are present
        let params: HashMap<String, String> = HashMap::new();
        
        let multi = params.get("multiMerchant").map(|v| v == "true").unwrap_or(false);
        let mca = params.get("mca").map(|v| v == "true").unwrap_or(false);
        
        let endpoint = if multi {
            "externalDsMultiMerchantDeposits"
        } else if mca {
            "externalDsMcaMerchantDeposits"
        } else {
            "externalDsMerchantDepositAggregates"
        };
        
        assert_eq!(endpoint, "externalDsMerchantDepositAggregates");
        assert!(!multi);
        assert!(!mca);
    }

    #[test]
    fn test_mca_with_terminal_id() {
        // Test that MCA mode works with terminalId parameter
        let mut params = HashMap::new();
        params.insert("mca".to_string(), "true".to_string());
        params.insert("terminalId".to_string(), "CXBH0006".to_string());
        
        let multi = params.get("multiMerchant").map(|v| v == "true").unwrap_or(false);
        let mca = params.get("mca").map(|v| v == "true").unwrap_or(false);
        let terminal_id = params.get("terminalId");
        
        let endpoint = if multi {
            "externalDsMultiMerchantDeposits"
        } else if mca {
            "externalDsMcaMerchantDeposits"
        } else {
            "externalDsMerchantDepositAggregates"
        };
        
        assert_eq!(endpoint, "externalDsMcaMerchantDeposits");
        assert!(!multi);
        assert!(mca);
        assert_eq!(terminal_id, Some(&"CXBH0006".to_string()));
    }

    #[test]
    fn test_mca_false_uses_default() {
        // Test that mca=false uses default endpoint
        let mut params = HashMap::new();
        params.insert("mca".to_string(), "false".to_string());
        
        let multi = params.get("multiMerchant").map(|v| v == "true").unwrap_or(false);
        let mca = params.get("mca").map(|v| v == "true").unwrap_or(false);
        
        let endpoint = if multi {
            "externalDsMultiMerchantDeposits"
        } else if mca {
            "externalDsMcaMerchantDeposits"
        } else {
            "externalDsMerchantDepositAggregates"
        };
        
        assert_eq!(endpoint, "externalDsMerchantDepositAggregates");
        assert!(!multi);
        assert!(!mca);
    }
}