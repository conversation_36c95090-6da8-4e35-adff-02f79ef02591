// # handlers/mod.rs
//
// HTTP Request Handlers Module
//
// This module serves as the entry point for organizing HTTP request handlers.
// Currently, most feature-specific handlers are organized in their respective
// feature modules (e.g., deposits::handlers), keeping this module clean and focused.
//
// ## Purpose
// Reserved for future non-feature-specific handlers such as:
// - Generic utility handlers
// - Cross-cutting concern handlers
// - Shared middleware handlers
//
// ## Current Architecture
// Feature-specific handlers are located in their respective modules:
// - `deposits::handlers` - Deposits-related HTTP handlers
// - `auth` - Authentication handlers (if moved from root)
// - `monitoring` - Health and monitoring handlers (if moved from root)
//
// This modular approach promotes code organization and maintainability. 