# Environment Configuration
ENVIRONMENT=dev

# Server Configuration
PORT=8000

# Database Configuration
DATABASE_URL=sqlite://deposits.db

# External API Configuration
# Deposits API Configuration
DEPOSITS_EXTERNAL_API_URL=https://merchant-deposits-myatm-api.paycorp.co.za
DEPOSITS_EXTERNAL_API_TIMEOUT=30

# Authentication
JWT_SECRET=your-secret-key
JWT_SIGNING_KEY=your-jwt-signing-key-here

# Syncfusion License
SYNCFUSION_LICENSE_KEY=Ngo9BigBOggjHTQxAR8/V1JEaF5cXmRCf1FpRmJGdld5fUVHYVZUTXxaS00DNHVRdkdmWXhec3RSRGlZU010XEFWYEE=

# APM Configuration
APM_SERVICE_NAME=merchant-deposits-myatm-api
APM_SERVER_URL=http://devappmon01.devprod.co.za:8200
APM_SECRET_TOKEN=SomeSecureString
APM_TRANSACTION_SAMPLE_RATE=1.0
APM_TRANSACTION_MAX_SPANS=500
APM_SERVICE_VERSION=1.0.0
APM_ENV=Dev
APM_LOG_LEVEL=Error

# Elasticsearch Configuration
ELASTICSEARCH_URL=http://devappmon01.devprod.co.za:9200
ELASTICSEARCH_APIKEY=YXBwbG9nZ2luZzo0RlE2ZXNyRGozTlpQNXR0
ELASTICSEARCH_INDEX_PREFIX=myatm-merchant-deposits-api-logs 