// # router.js
//
// Simple SPA router for handling client-side navigation.
// This module provides basic routing functionality for single-page applications.

class SPARouter {
    constructor() {
        this.routes = {};
        this.currentPath = '';
        this.currentQuery = {};
        this.initialized = false;
    }

    // Register a route with its handler
    register(path, handler) {
        this.routes[path] = handler;
    }

    // Parse query parameters from URL
    parseQueryParams(search = window.location.search) {
        const params = {};
        if (search) {
            // Remove the leading '?' and split by '&'
            const searchParams = new URLSearchParams(search);
            for (const [key, value] of searchParams) {
                params[key] = value;
            }
        }
        return params;
    }

    // Get current query parameters
    getQueryParams() {
        return this.currentQuery;
    }

    // Get a specific query parameter
    getQueryParam(key) {
        return this.currentQuery[key] || null;
    }

    // Navigate to a specific path
    navigate(path) {
        if (this.currentPath === path) {
            return; // Already on this path
        }

        console.log(`Navigating to: ${path}`);
        this.currentPath = path;
        
        // Update browser history
        window.history.pushState({}, '', path);
        
        // Handle the route
        this.handleRoute(path);
    }

    // Handle route change
    handleRoute(path) {
        console.log(`Handling route: ${path}`);
        
        // Parse current URL for path and query parameters
        const url = new URL(window.location);
        const pathname = url.pathname;
        this.currentQuery = this.parseQueryParams(url.search);
        
        console.log('Current query parameters:', this.currentQuery);
        
        // Find matching route handler
        const handler = this.routes[pathname];
        
        if (handler) {
            try {
                handler();
            } catch (error) {
                console.error(`Error handling route ${pathname}:`, error);
                this.showError(`Failed to load page: ${pathname}`);
            }
        } else {
            console.warn(`No handler found for route: ${pathname}`);
            this.showNotFound(pathname);
        }
    }

    // Show error page
    showError(message) {
        const content = document.getElementById('main-content') || document.body;
        content.innerHTML = `
            <div class="error-page">
                <h1>Error</h1>
                <p>${message}</p>
                <button onclick="router.navigate('/')">Go Home</button>
            </div>
        `;
    }

    // Show 404 page
    showNotFound(path) {
        const content = document.getElementById('main-content') || document.body;
        content.innerHTML = `
            <div class="not-found-page">
                <h1>Page Not Found</h1>
                <p>The page "${path}" was not found.</p>
                <button onclick="router.navigate('/')">Go Home</button>
            </div>
        `;
    }

    // Initialize the router
    init() {
        if (this.initialized) {
            return;
        }

        console.log('Initializing SPA router');
        
        // Handle browser back/forward buttons
        window.addEventListener('popstate', (event) => {
            const path = window.location.pathname;
            console.log(`Browser navigation to: ${path}`);
            this.handleRoute(path);
        });

        // Handle initial page load
        const initialPath = window.location.pathname;
        console.log(`Initial page load: ${initialPath}`);
        this.handleRoute(initialPath);

        this.initialized = true;
    }

    // Get current path
    getCurrentPath() {
        return this.currentPath;
    }
}

// Create global router instance
const router = new SPARouter();

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = router;
} else if (typeof window !== 'undefined') {
    window.router = router;
}

export default router; 