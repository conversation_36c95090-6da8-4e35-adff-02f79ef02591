// # app.js
//
// Main application entry point with modular routing system
// This file provides a scalable architecture for adding new pages

// Page registry for easy extensibility
const pageRegistry = new Map();

// Import Syncfusion utilities
import { registerSyncfusionLicense } from './utils/syncfusion-utils.js';

// Register license and then initialize the application
registerSyncfusionLicense().then(() => {
    initializeApplication();
});

function initializeApplication() {
    console.log('Initializing SPA application');
    
    // Import router and page modules
    import('./router.js').then(({ default: router }) => {
        // Removed dynamic import of deposits.js
        // Register pages in the registry (deposits page will only be available via deposits.html)
        registerPages();
        // Setup routing
        setupRouting(router);
    });
}

// Register all available pages
function registerPages() {
    // Register home page
    pageRegistry.set('home', {
        title: 'Home',
        path: '/',
        icon: '🏠',
        description: 'Application home page',
        handler: () => showHomePage()
    });
    // Register user directory (legacy feature)
    pageRegistry.set('users', {
        title: 'User Directory',
        path: '/users',
        icon: '👥',
        description: 'View user information',
        handler: () => showUserDirectory()
    });
    console.log(`📄 Registered ${pageRegistry.size} pages:`, Array.from(pageRegistry.keys()));
}

function setupRouting(router) {
    console.log('Setting up application routing');
    
    // Create main content area if it doesn't exist
    let mainContent = document.getElementById('main-content');
    if (!mainContent) {
        mainContent = document.createElement('div');
        mainContent.id = 'main-content';
        mainContent.className = 'main-content';
        
        // Replace the existing Grid div with main content
        const gridDiv = document.getElementById('Grid');
        if (gridDiv) {
            gridDiv.parentNode.replaceChild(mainContent, gridDiv);
        } else {
            document.body.appendChild(mainContent);
        }
    }
    
    // Register routes from page registry
    pageRegistry.forEach((page, key) => {
        router.register(page.path, page.handler);
        console.log(`🔗 Registered route: ${page.path} -> ${page.title}`);
    });
    
    // Initialize router
    router.init();
}

function showHomePage() {
    console.log('Showing home page');
    
    const mainContent = document.getElementById('main-content');
    
    // Generate navigation cards dynamically from page registry
    const navigationCards = Array.from(pageRegistry.entries())
        .filter(([key, page]) => key !== 'home') // Don't show home page in navigation
        .map(([key, page]) => `
            <div class="nav-card" onclick="router.navigate('${page.path}')">
                <div class="card-icon">${page.icon}</div>
                <h3>${page.title}</h3>
                <p>${page.description}</p>
            </div>
        `).join('');
    
    mainContent.innerHTML = `
        <div class="home-page">
            <div class="page-header">
                <h1>MyATM Embedded UI</h1>
                <p>Welcome to the MyATM management application</p>
            </div>
            
            <div class="navigation-cards">
                ${navigationCards}
            </div>
        </div>
    `;
    
    // Add styles for home page
    addHomePageStyles();
}

function addHomePageStyles() {
    const styles = `
        <style>
        .home-page {
            padding: 20px;
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .page-header {
            text-align: center;
            margin-bottom: 40px;
            padding: 40px 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 12px;
        }
        
        .page-header h1 {
            margin: 0;
            font-size: 2.5em;
            font-weight: 300;
        }
        
        .page-header p {
            margin: 10px 0 0 0;
            font-size: 1.2em;
            opacity: 0.9;
        }
        
        .navigation-cards {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 40px;
        }
        
        .nav-card {
            background: white;
            border-radius: 12px;
            padding: 30px;
            text-align: center;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .nav-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        }
        
        .card-icon {
            font-size: 3em;
            margin-bottom: 15px;
        }
        
        .nav-card h3 {
            margin: 0 0 10px 0;
            color: #333;
            font-size: 1.5em;
        }
        
        .nav-card p {
            margin: 0;
            color: #666;
            font-size: 1.1em;
        }
        
        .main-content {
            min-height: 100vh;
            background: #f8f9fa;
        }
        
        #user-directory {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        </style>
    `;
    
    // Add styles to document head
    const head = document.head || document.getElementsByTagName('head')[0];
    const existingStyle = head.querySelector('style[data-home-page]');
    
    if (existingStyle) {
        existingStyle.remove();
    }
    
    const styleElement = document.createElement('style');
    styleElement.setAttribute('data-home-page', 'true');
    styleElement.innerHTML = styles;
    head.appendChild(styleElement);
}

function showDepositsPage(depositsModule) {
    console.log('Showing deposits page');
    
    // Clean up any existing page state
    if (depositsModule.initialized) {
        depositsModule.destroy();
    }
    
    // Initialize deposits page
    depositsModule.init();
}

function showUserDirectory() {
    console.log('Showing user directory page');
    
    const mainContent = document.getElementById('main-content');
    mainContent.innerHTML = `
        <div class="user-directory-page">
            <div class="page-header">
                <h1>User Directory</h1>
                <p>Manage user information and access</p>
            </div>
            
            <div class="page-content">
                <div id="user-directory">
                    <div id="Grid"></div>
                </div>
            </div>
        </div>
    `;
    
    // Load user data
    loadUserData();
}

function loadUserData() {
    fetch('/api/data')
        .then(response => {
            if (!response.ok) throw new Error(`Server error: ${response.status}`);
            return response.json();
        })
        .then(data => {
            const flatData = data.map(item => ({
                ...item,
                company: item.company?.name || ''
            }));

            new ej.grids.Grid({
                dataSource: flatData,
                columns: [
                    { field: 'name', headerText: 'Name', width: 150 },
                    { field: 'username', headerText: 'Username', width: 150 },
                    { field: 'email', headerText: 'Email', width: 200 },
                    { field: 'company', headerText: 'Company', width: 200 }
                ],
                height: 400,
                allowPaging: true,
                pageSettings: { pageSize: 5 }
            }).appendTo('#Grid');
        })
        .catch(error => {
            console.error('Error loading data:', error);
            document.getElementById('Grid').innerHTML = '<p style="color:red;">Failed to load data.</p>';
        });
}

// Export page registry for external use
window.pageRegistry = pageRegistry;