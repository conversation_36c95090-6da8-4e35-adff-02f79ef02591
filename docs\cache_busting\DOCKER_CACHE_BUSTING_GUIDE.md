# Docker Cache Busting Guide

This guide explains how to use Docker builds with automatic cache busting for your JavaScript files.

## Overview

The Docker build process now includes automatic cache busting that:
- Processes frontend files during the Docker build
- Applies cache version parameters to all JavaScript files
- Uses build arguments to control versioning
- Works seamlessly with your existing Docker workflows

## Docker Build Process

### Enhanced Multi-Stage Build

The Dockerfile now includes three stages:

1. **Builder Stage**: Compiles the Rust backend
2. **Frontend Builder Stage**: Processes frontend files with cache busting
3. **Runtime Stage**: Creates the final minimal image

### Cache Busting Implementation

The frontend builder stage:
- Copies frontend files to a temporary container
- Applies cache busting using `sed` to modify HTML files
- Generates timestamp-based versions if not provided
- Outputs processed files for the final image

## Usage

### Basic Build

```bash
# Using PowerShell (Windows)
./docker-build.ps1

# Using Bash (Linux/macOS)
./docker-build.sh
```

### Advanced Build Options

```bash
# Build with specific version
./docker-build.ps1 -Version "1.2.3"
./docker-build.sh -v "1.2.3"

# Build with custom tag
./docker-build.ps1 -Tag "myapp:dev"
./docker-build.sh -t "myapp:dev"

# Build and push to registry
./docker-build.ps1 -Registry "registry.example.com" -Push
./docker-build.sh -r "registry.example.com" --push

# Build without cache
./docker-build.ps1 -NoCache
./docker-build.sh --no-cache
```

### Manual Docker Build

```bash
# Build with automatic timestamp
docker build --build-arg CACHE_VERSION=$(date +%Y%m%d%H%M%S) -t myatm-embedded-ui:latest .

# Build with specific version
docker build --build-arg CACHE_VERSION="1.2.3" -t myatm-embedded-ui:latest .

# Build without cache version (uses timestamp)
docker build -t myatm-embedded-ui:latest .
```

## Build Arguments

| Argument | Description | Default |
|----------|-------------|---------|
| `CACHE_VERSION` | Version string for cache busting | Current timestamp |
| `RUST_VERSION` | Rust version for builder | 1.87 |
| `MUSL_TARGET` | Target architecture | x86_64-unknown-linux-musl |

## CI/CD Integration

### GitHub Actions Example

```yaml
name: Build and Deploy

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]

jobs:
  build:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v4
    
    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3
    
    - name: Generate cache version
      id: cache-version
      run: echo "version=$(date +%Y%m%d%H%M%S)-${{ github.sha }}" >> $GITHUB_OUTPUT
    
    - name: Build Docker image
      run: |
        docker build \
          --build-arg CACHE_VERSION=${{ steps.cache-version.outputs.version }} \
          -t myatm-embedded-ui:${{ github.sha }} \
          .
    
    - name: Test the image
      run: |
        docker run --rm -d -p 8000:8000 --name test-container myatm-embedded-ui:${{ github.sha }}
        sleep 5
        curl -f http://localhost:8000/health/live || exit 1
        docker stop test-container
```

### Azure DevOps Example

```yaml
trigger:
- main

pool:
  vmImage: 'ubuntu-latest'

variables:
  imageRepository: 'myatm-embedded-ui'
  containerRegistry: 'myregistry.azurecr.io'
  dockerfilePath: 'Dockerfile'
  tag: '$(Build.BuildId)'
  cacheVersion: '$(Build.BuildId)-$(Build.SourceVersion)'

steps:
- task: Docker@2
  displayName: 'Build Docker image'
  inputs:
    command: 'build'
    dockerfile: '$(dockerfilePath)'
    repository: '$(imageRepository)'
    tags: '$(tag)'
    arguments: '--build-arg CACHE_VERSION=$(cacheVersion)'
```

## Development Workflow

### Local Development

1. **Make changes** to your frontend files
2. **Build Docker image** with cache busting:
   ```bash
   ./docker-build.ps1 -Version "dev-$(Get-Date -Format 'HHmmss')"
   ```
3. **Run the container**:
   ```bash
   docker run -p 8000:8000 myatm-embedded-ui:latest
   ```
4. **Test in browser** - cache busting is automatically applied

### Production Deployment

1. **Build with semantic version**:
   ```bash
   ./docker-build.ps1 -Version "1.2.3" -Tag "myatm-embedded-ui:1.2.3"
   ```
2. **Push to registry**:
   ```bash
   ./docker-build.ps1 -Version "1.2.3" -Registry "registry.example.com" -Push
   ```
3. **Deploy to production** using the tagged image

## Verification

### Check Cache Busting Applied

```bash
# Run a temporary container and check the HTML
docker run --rm myatm-embedded-ui:latest cat /static/index.html | grep -E "\.js\?v="

# Expected output:
# <script src="app.js?v=20241201123456"></script>
```

### Test in Browser

1. Open browser developer tools
2. Go to Network tab
3. Load your application
4. Check that JavaScript files have version parameters
5. Verify cache busting by looking at request URLs

## Integration with Existing Scripts

The Docker build process is compatible with your existing cache busting scripts:

```bash
# Option 1: Use local scripts then Docker build
./update-cache-version.ps1 -Version "1.2.3"
docker build -t myatm-embedded-ui:latest .

# Option 2: Let Docker handle everything (recommended)
./docker-build.ps1 -Version "1.2.3"
```

## Troubleshooting

### Common Issues

1. **Cache version not applied**:
   - Check that the `CACHE_VERSION` build arg is passed correctly
   - Verify the frontend builder stage logs during build

2. **Build fails at frontend stage**:
   - Ensure `frontend/index.html` exists and is valid
   - Check that the `sed` command syntax is correct for your HTML structure

3. **JavaScript files not loading**:
   - Verify that the cache-busted URLs are correct
   - Check that the static files are properly copied to the container

### Debug Build Process

```bash
# Build with verbose output
docker build --build-arg CACHE_VERSION=debug --progress=plain -t myatm-embedded-ui:debug .

# Inspect the frontend builder stage
docker build --target frontend-builder -t frontend-debug .
docker run --rm frontend-debug cat index.html
```

## Best Practices

1. **Use semantic versioning** for production builds
2. **Include git commit hash** in CI/CD builds for traceability
3. **Test cache busting** in staging environments
4. **Monitor build times** - cache busting adds minimal overhead
5. **Use build caching** to speed up repeated builds

## File Structure

```
├── Dockerfile                        # Enhanced with cache busting
├── docker-build.ps1                  # PowerShell build script
├── docker-build.sh                   # Bash build script
├── frontend/                         # Source files
│   ├── index.html
│   └── app.js
└── backend/                          # Rust backend
    ├── src/
    └── static/                       # Generated during Docker build
```

## Environment Variables

The Docker container can use these environment variables:

| Variable | Description | Default |
|----------|-------------|---------|
| `ENVIRONMENT` | Runtime environment | development |
| `RUST_LOG` | Logging level | info |

## Security Considerations

- Cache busting parameters are safe and don't expose sensitive information
- Build arguments are not stored in the final image layers
- Static files are processed during build, not runtime
- No additional attack surface is introduced

## Performance Impact

- **Build time**: +5-10 seconds for frontend processing
- **Image size**: No significant change
- **Runtime**: No performance impact
- **Caching**: Improved cache invalidation for users 