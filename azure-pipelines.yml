# Docker Pipeline
# Builds and pushes a Docker image to the container registry
# https://docs.microsoft.com/azure/devops/pipelines/languages/docker

trigger:
  branches:
    include:
      - master
      - feat/*

resources:
  repositories:
    - repository: self
      type: git
      ref: refs/heads/master

name: $(date:yyyyMMdd)$(rev:.r)

variables:
  - group: HarborPull
  - name: tag
    value: '$(Build.BuildId)'

stages:
- stage: BuildAndPush
  displayName: Build and Push Docker Image
  jobs:
  - job: DockerJob
    displayName: Build and Push
    pool:
      name: 'Rancher or Docker'
    steps:
    - checkout: self
      fetchDepth: 1

    - script: |
        echo "Setting up Docker inline auth for Harbor..."
        mkdir -p $(Build.SourcesDirectory)/.docker
        echo '{ "auths": { "dockerregistry.paycorp.co.za": { "username": "$(HARBOR_USER)", "password": "$(HARBOR_PASSWORD)" } } }' > $(Build.SourcesDirectory)/.docker/config.json
      displayName: Create Docker Auth Config

    - script: |
        DOCKER_CONFIG=$(Build.SourcesDirectory)/.docker \
        docker build \
          --no-cache \
          -f $(Build.SourcesDirectory)/Dockerfile \
          -t dockerregistry.paycorp.co.za/crmuis/myatm-embedded-ui:$(tag) \
          -t dockerregistry.paycorp.co.za/crmuis/myatm-embedded-ui:latest \
          $(Build.SourcesDirectory)
      displayName: Build Docker Image with Inline Auth

    - task: Docker@2
      displayName: Push Docker Image
      inputs:
        command: push
        containerRegistry: fa47b53c-12f3-469b-9905-9c1517ac1a1a
        repository: crmuis/myatm-embedded-ui
        tags: |
          $(tag)
          latest
