apiVersion: v1
kind: ConfigMap
metadata:
  name: myatm-embedded-ui-inline-otel-config
  namespace: crmuis
data:
  otel-collector-config.yaml: |-
    receivers:
      otlp:
        protocols:
          http:
            endpoint: 0.0.0.0:4318
          grpc:
            endpoint: 0.0.0.0:4317

    processors:
      # Add resource attributes for better service identification
      resource:
        attributes:
          - key: service.name
            value: myatm-embedded-ui
            action: upsert
          - key: deployment.environment
            value: QA
            action: upsert

    exporters:
      elasticsearch:
        endpoints: ["https://qaprdelk0.qaprod.co.za:9200"]
        headers:
          Authorization: "ApiKey YXBwbG9nZ2luZzo0RlE2ZXNyRGozTlpQNXR0"
        index: "myatm-embedded-ui-logs-%{+yyyy.MM.dd}"

      otlp/elastic:
        endpoint: "https://qaprdelk0.qaprod.co.za:8200"
        headers:
          Authorization: "Bearer 478K1%hq9yms"
        tls:
          insecure: false

      # Add debug logging for troubleshooting
      logging:
        loglevel: debug

    service:
      pipelines:
        traces:
          receivers: [otlp]
          processors: [resource]
          exporters: [otlp/elastic, logging]

        metrics:
          receivers: [otlp]
          processors: [resource]
          exporters: [otlp/elastic, logging]

        logs:
          receivers: [otlp]
          processors: [resource]
          exporters: [elasticsearch, logging]
