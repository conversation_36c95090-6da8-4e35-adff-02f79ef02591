/* # styles.css */
/*
 * Global Application Styles - MyATM Embedded UI
 *
 * This stylesheet provides the base styling for the MyATM Embedded UI application.
 * It includes global styles, utility classes, and foundational design elements
 * that are used across the entire application.
 *
 * ## Style Organization
 * - **Base Styles**: Typography, layout fundamentals
 * - **Cache Management**: Notification styles for cache updates
 * - **Development Tools**: Development-only UI controls
 * - **Responsive Design**: Mobile-first responsive utilities
 *
 * ## Design System
 * The styles follow a consistent design system with:
 * - Primary color: #007bff (blue)
 * - Success color: #4CAF50 (green)
 * - Info color: #2196F3 (light blue)
 * - Error color: #f44336 (red)
 * - Font family: system sans-serif stack
 *
 * ## Feature-Specific Styles
 * Feature-specific styles are maintained in separate files:
 * - deposits.css: Deposits page styling
 * - Individual component styles as needed
 *
 * ## Production Considerations
 * - Development controls are automatically hidden in production
 * - Cache notifications are optimized for performance
 * - Responsive design ensures mobile compatibility
 */

/* Base application styles */
body {
    font-family: sans-serif;
    margin-top: 2px;
    margin-left: 5px;
    margin-right: 5px;
    margin-bottom: 0px;
}

#Grid {
    max-width: 900px;
    margin: auto;
}

.cache-notification {
    position: fixed;
    top: 20px;
    right: 20px;
    background: #4CAF50;
    color: white;
    padding: 12px 20px;
    border-radius: 6px;
    font-size: 14px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    z-index: 10000;
    opacity: 0;
    transform: translateX(100%);
    transition: all 0.3s ease-in-out;
    max-width: 300px;
    display: none; /* Hidden by default - only shown when explicitly triggered */
}

.cache-notification.show {
    opacity: 1;
    transform: translateX(0);
    display: block;
}

.cache-notification.updating {
    background: #2196F3;
}

.cache-notification.error {
    background: #f44336;
}

.dev-controls {
    position: fixed;
    bottom: 10px;
    right: 10px;
    background: #007bff;
    color: white;
    padding: 10px;
    border-radius: 5px;
    font-size: 12px;
    z-index: 1000;
}

.dev-controls button {
    background: white;
    color: #007bff;
    border: none;
    padding: 5px 10px;
    margin: 2px;
    border-radius: 3px;
    cursor: pointer;
    font-size: 11px;
}

.dev-controls button:hover {
    background: #f0f0f0;
}

/* Hide dev controls in production */
@media (min-width: 1px) {
    .dev-controls[data-production="true"] {
        display: none;
    }
} 