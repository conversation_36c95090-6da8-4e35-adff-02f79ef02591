// # main.rs
//
// MyATM Embedded UI - Backend API Server
//
// This is the main entry point for the Rust backend server that serves as a Backend-for-Frontend (BFF)
// for the MyATM Embedded UI application. The server provides:
//
// ## Core Features
// - **Static File Serving**: Serves the built frontend SPA with cache-busting support
// - **API Proxy**: Proxies requests to external services (e.g., deposits API)
// - **Health Checks**: Kubernetes-ready health endpoints
// - **Authentication**: JWT-based authentication middleware
// - **Observability**: OpenTelemetry tracing, Elasticsearch logging, and metrics
// - **Security**: Comprehensive security headers and CORS support
// - **Performance**: Request compression, timeouts, and body limits
//
// ## Architecture
// The server uses Axum as the web framework with a modular design:
// - `/api/*` - API endpoints and proxies
// - `/health/*` - Health check endpoints
// - `/static/*` - Static assets with cache busting
// - `/*` - SPA fallback routing
//
// ## Key Middleware Stack (applied in order)
// 1. Request body limits (10MB)
// 2. Request timeouts (30s)
// 3. OpenTelemetry tracing
// 4. Request ID generation
// 5. Security headers (CSP, XSS protection, etc.)
// 6. Response compression (gzip, brotli, zstd)
//
// ## Environment Configuration
// - PORT: Server port (default: 8000)
// - ENVIRONMENT: Runtime environment (development/production)
// - CACHE_VERSION: Static asset cache version
// - DEPOSITS_EXTERNAL_API_URL: External deposits API endpoint
// - JWT_SECRET: JWT authentication secret
//
// ## Development vs Production
// In development, loads .env file; in production, uses environment variables directly.

use axum::{
    http::StatusCode,
    response::{IntoResponse, Response, Html},
    routing::get,
    Json, Router,
};
use serde_json::{json, Value};
use std::net::SocketAddr;
use std::time::Duration;
use tokio::net::TcpListener;
use tower_http::{
    trace::TraceLayer,
    compression::CompressionLayer,
    set_header::SetResponseHeaderLayer,
    timeout::TimeoutLayer,
    limit::RequestBodyLimitLayer,
    request_id::{MakeRequestId, RequestId, SetRequestIdLayer},
    services::ServeFile,
};
use axum::http::{header, HeaderValue};
use tracing::{info, instrument, span, Level};
use uuid::Uuid;

mod monitoring;
mod elasticsearch_layer;
mod static_files;
mod cache_middleware;
mod auth;
mod config;
mod models;
mod handlers;
mod deposits;

use cache_middleware::CacheBustingState;
use static_files::{serve_index, serve_static_file, serve_app_js, serve_cache_client_js, serve_spa_fallback};
use config::AppConfig;


/// Request ID generator for tracing
#[derive(Clone)]
struct UuidMakeRequestId;

impl MakeRequestId for UuidMakeRequestId {
    fn make_request_id<B>(&mut self, _request: &axum::http::Request<B>) -> Option<RequestId> {
        let request_id = Uuid::new_v4().to_string();
        Some(RequestId::new(request_id.parse().unwrap()))
    }
}



#[tokio::main]
#[instrument]
async fn main() {
    // Load environment variables from .env file in development
    // In production, use OS environment variables
    // First try to load .env file, then check environment setting
    if let Err(e) = dotenvy::from_filename("../.env") {
        // Fallback to .env in current directory if ../.env doesn't exist
        if let Err(e2) = dotenvy::dotenv() {
            eprintln!("Warning: Could not load .env file: {} (tried ../.env: {})", e2, e);
        }
    }
    
    // Log which environment we're running in after loading .env
    let environment = std::env::var("ENVIRONMENT").unwrap_or_else(|_| "development".to_string());
    info!("Running in environment: {}", environment);

    // Initialize monitoring (OpenTelemetry, tracing, Elasticsearch, metrics)
    if let Err(e) = monitoring::setup_monitoring().await {
        eprintln!("Failed to initialize monitoring: {e}");
    }
    
    info!("Application starting up with tracing enabled");

    // Load application configuration
    let app_config = AppConfig::from_env().expect("Failed to load application configuration");

    // Print out the deposits API base URL being used
    info!("🌐 Deposits API base URL: {}", app_config.deposits.external_api.base_url);

    // Initialize cache busting
    let cache_version_env = std::env::var("CACHE_VERSION").ok();
    let cache_state = CacheBustingState::new(cache_version_env);
    
    // Initialize static files cache busting
    static_files::init_cache_busting(Some(cache_state.version.clone()));

    // Create enhanced router with cache busting
    let app = Router::new()
        // API routes
        .route("/api/data", get(proxy_users))
        .route("/api/syncfusion-license", get(get_syncfusion_license))
        .route("/api/trace-test", get(trace_test))
        .route("/health/live", get(health_live))
        .route("/health/ready", get(health_ready))
        .route("/api/performance-test", get(performance_test))
        
        // Deposits proxy route (no auth required - BFF handles external auth internally)
        .route("/api/deposits", get(deposits::handlers::deposits_proxy))
        .route("/api/deposits/health", get(deposits::handlers::deposits_health))
        
        // Cache busting API route
        .route("/api/cache/version", get(cache_version))
        
        // Serve standalone deposits page
        .route_service("/deposits", ServeFile::new("static/deposits/deposits.html"))
        
        // Cache busting routes will be added separately
        // TODO: Integrate cache busting with AppState
        
        // Custom static file routes with cache busting
        .route("/", get(serve_index))
        .route("/index.html", get(serve_index))
        .route("/static/*path", get(serve_static_file))
        
        // Direct routes for JS files (requested at root level)
        .route("/app.js", get(serve_app_js))
        .route("/cache-client.js", get(serve_cache_client_js))
        
        // SPA fallback - serves static files first, then index.html for routes
        .fallback(serve_spa_fallback)
        
        // Add state to router
        .with_state(app_config.clone())
        
        // Apply middleware layers individually to avoid compatibility issues
        
        // Compression layer (applied first for best performance)
        .layer(CompressionLayer::new()
            .gzip(true)
            .br(true)
            .zstd(true)
        )
        
        // Security headers
        .layer(SetResponseHeaderLayer::overriding(
            header::X_CONTENT_TYPE_OPTIONS,
            HeaderValue::from_static("nosniff"),
        ))
        // X-FRAME-OPTIONS removed to avoid conflicts with CSP frame-ancestors
        // CSP frame-ancestors * allows any iframe embedding
        .layer(SetResponseHeaderLayer::overriding(
            header::HeaderName::from_static("content-security-policy"),
            HeaderValue::from_static("default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://cdn.syncfusion.com; style-src 'self' 'unsafe-inline' https://cdn.syncfusion.com https://fonts.googleapis.com; img-src 'self' data:; font-src 'self' data: https://cdn.syncfusion.com https://fonts.gstatic.com; connect-src 'self'; frame-src 'none'; frame-ancestors *; object-src 'none'; base-uri 'self';"),
        ))
        .layer(SetResponseHeaderLayer::overriding(
            header::HeaderName::from_static("x-xss-protection"),
            HeaderValue::from_static("1; mode=block"),
        ))
        .layer(SetResponseHeaderLayer::overriding(
            header::REFERRER_POLICY,
            HeaderValue::from_static("strict-origin-when-cross-origin"),
        ))
        .layer(SetResponseHeaderLayer::overriding(
            header::HeaderName::from_static("permissions-policy"),
            HeaderValue::from_static("geolocation=(), microphone=(), camera=()"),
        ))
        
        // Performance headers
        .layer(SetResponseHeaderLayer::overriding(
            header::HeaderName::from_static("x-powered-by"),
            HeaderValue::from_static("TurboEncabulator v3.14-RC2"),
        ))
        .layer(SetResponseHeaderLayer::overriding(
            header::SERVER,
            HeaderValue::from_static("FluxCapacitor/8.8.8"),
        ))
        
        // Request ID layer
        .layer(SetRequestIdLayer::new(
            header::HeaderName::from_static("x-request-id"),
            UuidMakeRequestId,
        ))
        
        // OpenTelemetry tracing layer
        .layer(TraceLayer::new_for_http())
        
        // Cache busting middleware - disabled for now
        // .layer(middleware::from_fn_with_state(
        //     cache_state.clone(),
        //     cache_busting_middleware,
        // ))
        
        // Request timeout (applied towards the end)
        .layer(TimeoutLayer::new(Duration::from_secs(30)))
        
        // Request body limit (applied last)
        .layer(RequestBodyLimitLayer::new(10 * 1024 * 1024));

    let addr = SocketAddr::from(([0, 0, 0, 0], app_config.server.port));
    
    info!("🚀 Server starting on http://{addr}");
    info!("🔧 Cache version: {}", static_files::get_build_version());
    info!("📊 OpenTelemetry tracing enabled");
    info!("🗜️  Compression enabled: gzip, brotli, zstd");
    info!("🔒 Security headers enabled: XSS protection, content-type options, frame options");
    info!("⏱️  Request timeout: 30 seconds");
    info!("📏 Request body limit: 10MB");
    info!("🆔 Request ID tracking enabled");
    info!("📊 Cache busting endpoints:");
    info!("  - GET /api/cache/version    - Get current cache version and assets");
    info!("  - GET /api/cache/manifest   - Get asset manifest");
    info!("  - GET /api/cache/refresh    - Refresh cache (development)");
    info!("  - GET /api/cache/health     - Health check with cache info");

    let listener = TcpListener::bind(&addr).await.unwrap();
    info!("Server bound to address, starting to serve requests");
    axum::serve(listener, app).await.unwrap();
}



#[instrument]
async fn proxy_users() -> Response {
    info!("Proxying request to users API");
    match reqwest::get("https://jsonplaceholder.typicode.com/users").await {
        Ok(resp) => match resp.json::<Value>().await {
            Ok(json) => {
                info!("Successfully retrieved users data");
                Json(json).into_response()
            },
            Err(_) => {
                info!("Failed to parse JSON response");
                (StatusCode::BAD_GATEWAY, "Invalid JSON").into_response()
            },
        },
        Err(_) => {
            info!("Failed to reach users API");
            (StatusCode::BAD_GATEWAY, "API unreachable").into_response()
        },
    }
}

/// Syncfusion license key endpoint
/// Returns the Syncfusion license key from environment variables for frontend registration
#[instrument]
async fn get_syncfusion_license() -> Response {
    info!("Providing Syncfusion license key to frontend");
    
    match std::env::var("SYNCFUSION_LICENSE_KEY") {
        Ok(license_key) => {
            if license_key.trim().is_empty() {
                info!("Syncfusion license key is empty");
                (StatusCode::BAD_REQUEST, Json(json!({
                    "error": "Syncfusion license key is empty",
                    "status": "error",
                    "timestamp": chrono::Utc::now().to_rfc3339()
                }))).into_response()
            } else {
                info!("Successfully retrieved Syncfusion license key from environment");
                Json(json!({
                    "license_key": license_key,
                    "status": "success",
                    "timestamp": chrono::Utc::now().to_rfc3339()
                })).into_response()
            }
        },
        Err(_) => {
            info!("Syncfusion license key not found in environment variables");
            (StatusCode::NOT_FOUND, Json(json!({
                "error": "SYNCFUSION_LICENSE_KEY environment variable not set",
                "status": "error",
                "message": "Please set SYNCFUSION_LICENSE_KEY environment variable",
                "timestamp": chrono::Utc::now().to_rfc3339()
            }))).into_response()
        }
    }
}

/// Test endpoint to generate traces for debugging
#[instrument]
async fn trace_test() -> Response {
    info!("Starting trace test endpoint");
    
    // Create a manual span for testing
    let span = span!(Level::INFO, "trace_test_operation", operation = "test");
    let _enter = span.enter();
    
    info!("Creating nested operations");
    
    // Simulate some work with nested spans
    let result = {
        let nested_span = span!(Level::INFO, "nested_operation", step = "data_processing");
        let _enter = nested_span.enter();
        
        info!("Processing data in nested span");
        tokio::time::sleep(tokio::time::Duration::from_millis(100)).await;
        
        "Operation completed successfully"
    };
    
    info!("Trace test completed: {}", result);
    
    Json(json!({
        "status": "success",
        "message": "Trace test completed",
        "timestamp": chrono::Utc::now().to_rfc3339(),
        "result": result
    })).into_response()
}

/// Liveness probe endpoint for Kubernetes
/// Returns 200 OK if the application is running
#[instrument]
async fn health_live() -> impl IntoResponse {
    (StatusCode::OK, Json(json!({
        "status": "alive",
        "timestamp": chrono::Utc::now().to_rfc3339(),
        "cache_version": static_files::get_build_version()
    })))
}

/// Readiness probe endpoint for Kubernetes
/// Returns 200 OK if the application is ready to serve requests
#[instrument]
async fn health_ready() -> impl IntoResponse {
    // Add any additional readiness checks here (database connections, etc.)
    (StatusCode::OK, Json(json!({
        "status": "ready",
        "timestamp": chrono::Utc::now().to_rfc3339(),
        "cache_version": static_files::get_build_version()
    })))
}

/// Performance test endpoint
/// Returns a response with compressible content to test compression
#[instrument]
async fn performance_test() -> Response {
    info!("Starting performance test endpoint");
    
    // Generate a large amount of compressible content
    let compressible_text = "Hello World! This is a test string that should compress well when gzip is enabled. ".repeat(1000);
    
    info!("Generated compressible content of {} bytes", compressible_text.len());
    
    Json(json!({
        "status": "success",
        "message": "Performance test completed - check response headers for compression",
        "timestamp": chrono::Utc::now().to_rfc3339(),
        "content_length": compressible_text.len(),
        "compressible_content": compressible_text,
        "compression_info": {
            "original_size": compressible_text.len(),
            "note": "Check Content-Encoding header to verify compression is working"
        }
    })).into_response()
}

/// Cache version endpoint
/// Returns the current cache version and asset manifest
#[instrument]
async fn cache_version() -> Json<serde_json::Value> {
    info!("Serving cache version info");
    Json(json!({
        "version": static_files::get_build_version(),
        "assets": static_files::get_asset_manifest()
    }))
} 