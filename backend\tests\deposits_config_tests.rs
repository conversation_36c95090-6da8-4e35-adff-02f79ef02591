// deposits_config_tests.rs

//! Deposits Module Configuration Unit Tests
//! 
//! This module contains unit tests specifically for the deposits module configuration system
//! used in the ATM embedded UI backend. It validates the proper loading, parsing, and
//! defaulting of deposits-specific configuration values including external API settings,
//! timeout configurations, and merchant service endpoints.
//! 
//! ## Test Coverage:
//! 
//! ### 1. Deposits Configuration Loading
//! - Tests the `DepositsConfig::from_env()` functionality
//! - Validates that deposits-specific configuration can be loaded from environment variables
//! - Ensures proper error handling for deposits module configuration issues
//! 
//! ### 2. External API Configuration
//! - Verifies default timeout settings (30 seconds) for external merchant API calls
//! - Tests base URL configuration for merchant deposits API endpoints
//! - Validates connection parameters for third-party integrations
//! 
//! ### 3. Default Value Validation
//! - Ensures appropriate fallback values when environment variables are not set
//! - Tests default timeout configurations for API stability
//! - Validates default merchant API endpoint configurations
//! 
//! ## Test Scope:
//! 
//! ### Deposits Module Specifics:
//! - **External API Integration**: Tests configuration for merchant deposits API
//! - **Timeout Management**: Validates timeout settings for external service calls
//! - **Service Discovery**: Tests base URL configuration for merchant services
//! - **Error Handling**: Ensures proper fallback behavior for configuration failures
//! 
//! ## Test Philosophy:
//! 
//! - **Module Isolation**: Tests focus specifically on deposits module configuration
//! - **External Integration**: Validates configuration for third-party API connections
//! - **Timeout Safety**: Ensures reasonable default timeouts to prevent hanging requests
//! - **Service Reliability**: Tests configuration that ensures stable external API calls
//! 
//! ## Important Notes:
//! 
//! - **Default Timeout**: 30 seconds provides balance between responsiveness and reliability
//! - **Merchant API**: Base URL contains "merchant-deposits-myatm-api" for service identification
//! - **Environment Override**: Production deployments should override defaults via env vars
//! - **Network Resilience**: Configuration supports timeout-based fault tolerance
//! 
//! ## Configuration Dependencies:
//! 
//! These tests validate the deposits configuration system defined in `src/deposits/config.rs`:
//! - External API endpoint configurations
//! - Timeout and retry settings for merchant services
//! - Connection parameters for deposits processing
//! - Integration settings for third-party payment systems
//! 
//! ## Integration Context:
//! 
//! The deposits module configuration supports:
//! - ATM transaction processing via merchant APIs
//! - Real-time deposit validation and confirmation
//! - External payment service integration
//! - Fault-tolerant communication with banking systems
//! 
//! ## Running Tests:
//! 
//! ```bash
//! cargo test deposits_config_tests
//! ```
//! 
//! For verbose output:
//! ```bash
//! cargo test deposits_config_tests -- --nocapture
//! ```

use myatm_embedded_ui::deposits::config::*;

#[test]
fn test_deposits_config_from_env() {
    // Test with default values
    let config = DepositsConfig::from_env().unwrap();
    assert_eq!(config.external_api.timeout_seconds, 30);
    assert!(config.external_api.base_url.contains("merchant-deposits-myatm-api"));
} 