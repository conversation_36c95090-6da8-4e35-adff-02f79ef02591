# Test script to demonstrate Docker cache busting
param(
    [string]$TestVersion = "test-$(Get-Date -Format 'HHmmss')"
)

$ErrorActionPreference = "Stop"

Write-Host "🧪 Testing Docker Cache Busting..." -ForegroundColor Green
Write-Host "📦 Test version: $TestVersion" -ForegroundColor Cyan

try {
    # Build the Docker image with cache busting
    Write-Host "🔨 Building Docker image with cache busting..." -ForegroundColor Yellow
    & docker build --build-arg CACHE_VERSION=$TestVersion -t myatm-embedded-ui:test .
    
    if ($LASTEXITCODE -ne 0) {
        throw "Docker build failed"
    }
    
    # Check if cache busting was applied
    Write-Host "🔍 Checking if cache busting was applied..." -ForegroundColor Yellow
    $htmlContent = & docker run --rm myatm-embedded-ui:test cat /static/index.html
    
    if ($htmlContent -match "app\.js\?v=$TestVersion") {
        Write-Host "✅ Cache busting successfully applied!" -ForegroundColor Green
        Write-Host "🎯 Found: app.js?v=$TestVersion" -ForegroundColor Cyan
    } else {
        Write-Host "❌ Cache busting not found!" -ForegroundColor Red
        Write-Host "📄 HTML content preview:" -ForegroundColor Yellow
        $htmlContent | Select-String "script.*app\.js" | ForEach-Object { Write-Host $_.Line -ForegroundColor White }
        exit 1
    }
    
    # Test that the application starts
    Write-Host "🚀 Testing application startup..." -ForegroundColor Yellow
    $containerId = & docker run -d -p 8001:8000 myatm-embedded-ui:test
    
    if ($LASTEXITCODE -ne 0) {
        throw "Failed to start container"
    }
    
    Start-Sleep -Seconds 3
    
    # Test health endpoint
    try {
        $response = Invoke-WebRequest -Uri "http://localhost:8001/health/live" -UseBasicParsing -TimeoutSec 5
        if ($response.StatusCode -eq 200) {
            Write-Host "✅ Application is running successfully!" -ForegroundColor Green
            Write-Host "🏥 Health check passed" -ForegroundColor Cyan
        } else {
            Write-Host "⚠️  Application started but health check failed" -ForegroundColor Yellow
        }
    } catch {
        Write-Host "⚠️  Could not reach health endpoint: $_" -ForegroundColor Yellow
    }
    
    # Test main page
    try {
        $response = Invoke-WebRequest -Uri "http://localhost:8001/" -UseBasicParsing -TimeoutSec 5
        if ($response.Content -match "app\.js\?v=$TestVersion") {
            Write-Host "✅ Cache busting verified in served content!" -ForegroundColor Green
        } else {
            Write-Host "⚠️  Cache busting not found in served content" -ForegroundColor Yellow
        }
    } catch {
        Write-Host "⚠️  Could not reach main page: $_" -ForegroundColor Yellow
    }
    
    # Clean up
    Write-Host "🧹 Cleaning up..." -ForegroundColor Yellow
    & docker stop $containerId | Out-Null
    & docker rm $containerId | Out-Null
    
    Write-Host "🎉 Docker cache busting test completed successfully!" -ForegroundColor Green
    Write-Host "📋 Summary:" -ForegroundColor White
    Write-Host "  - Cache version: $TestVersion" -ForegroundColor White
    Write-Host "  - Docker image: myatm-embedded-ui:test" -ForegroundColor White
    Write-Host "  - Cache busting: ✅ Working" -ForegroundColor White
    Write-Host "  - Application: ✅ Running" -ForegroundColor White
    
} catch {
    Write-Host "❌ Test failed: $_" -ForegroundColor Red
    
    # Clean up on failure
    if ($containerId) {
        Write-Host "🧹 Cleaning up failed container..." -ForegroundColor Yellow
        & docker stop $containerId 2>$null | Out-Null
        & docker rm $containerId 2>$null | Out-Null
    }
    
    exit 1
} 