# Docker Cache Busting - Quick Start

## 🚀 What's Been Added

Your Docker build process now includes **automatic cache busting** for JavaScript files! 

## 📁 New Files Created

- **`Dockerfile`** (updated) - Enhanced with cache busting stage
- **`docker-build.ps1`** - PowerShell build script with cache busting
- **`docker-build.sh`** - Bash build script for Linux/macOS
- **`test-docker-cache-busting.ps1`** - Test script to verify functionality
- **`DOCKER_CACHE_BUSTING_GUIDE.md`** - Comprehensive documentation

## 🔧 How It Works

1. **Frontend Builder Stage**: Added to Dockerfile that processes HTML files
2. **Cache Versioning**: Uses build arguments to inject version parameters
3. **Automatic Processing**: Applies `?v=timestamp` to all JavaScript files
4. **No Runtime Impact**: All processing happens during Docker build

## 🎯 Quick Usage

### Option 1: Simple Build (Recommended)
```powershell
# Build with automatic timestamp
./docker-build.ps1
```

### Option 2: Versioned Build
```powershell
# Build with specific version
./docker-build.ps1 -Version "1.2.3"
```

### Option 3: Manual Docker Build
```bash
# Build with cache busting
docker build --build-arg CACHE_VERSION=$(date +%Y%m%d%H%M%S) -t myatm-embedded-ui:latest .
```

## 🧪 Test It

```powershell
# Test that cache busting works
./test-docker-cache-busting.ps1
```

## 🔍 What Happens

**Before** (in your HTML):
```html
<script src="app.js"></script>
```

**After** (in the Docker image):
```html
<script src="app.js?v=20241201123456"></script>
```

## 🎉 Benefits

- **Zero Runtime Overhead**: Processing happens during build, not when serving
- **Automatic Versioning**: No need to manually update version numbers
- **CI/CD Ready**: Easy to integrate with deployment pipelines
- **Backwards Compatible**: Works with existing deployment processes
- **Future-Proof**: Handles any JavaScript files you add

## 🚀 Production Deployment

```powershell
# Build for production
./docker-build.ps1 -Version "1.2.3" -Tag "myatm-embedded-ui:1.2.3"

# Or with registry push
./docker-build.ps1 -Version "1.2.3" -Registry "your-registry.com" -Push
```

## 📋 CI/CD Integration

Your existing CI/CD can now include cache busting:

```yaml
# Example for GitHub Actions
- name: Build Docker image
  run: |
    docker build \
      --build-arg CACHE_VERSION=${{ github.sha }} \
      -t myatm-embedded-ui:${{ github.sha }} \
      .
```

## 🛠️ Development Workflow

1. **Make changes** to `frontend/app.js`
2. **Build Docker image**: `./docker-build.ps1`
3. **Run container**: `docker run -p 8000:8000 myatm-embedded-ui:latest`
4. **Test in browser** - cache busting is automatically applied!

## 📖 More Information

- See `DOCKER_CACHE_BUSTING_GUIDE.md` for complete documentation
- See `CACHE_BUSTING_GUIDE.md` for non-Docker approaches
- Use `test-docker-cache-busting.ps1` to verify functionality

---

**Ready to use!** Your Docker builds now include automatic cache busting. 🎉 