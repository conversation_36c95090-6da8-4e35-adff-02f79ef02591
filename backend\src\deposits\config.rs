// # deposits/config.rs
//
// Deposits-specific configuration management.
// This module handles loading configuration specific to deposits functionality.

use serde::{Deserialize, Serialize};
use std::env;

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct DepositsConfig {
    pub external_api: DepositsExternalApiConfig,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct DepositsExternalApiConfig {
    pub base_url: String,
    pub timeout_seconds: u64,
}

impl DepositsConfig {
    pub fn from_env() -> Result<Self, Box<dyn std::error::Error>> {
        Ok(DepositsConfig {
            external_api: DepositsExternalApiConfig {
                base_url: env::var("DEPOSITS_EXTERNAL_API_URL")
                    .unwrap_or_else(|_| "https://merchant-deposits-myatm-api.qaoffice.co.za".to_string()),
                timeout_seconds: env::var("DEPOSITS_EXTERNAL_API_TIMEOUT")
                    .unwrap_or_else(|_| "30".to_string())
                    .parse()
                    .unwrap_or(30),
            },
        })
    }
}

 