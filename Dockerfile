###############################################################################
# configurable versions (override with --build-arg)
###############################################################################
ARG RUST_VERSION=1.88
ARG MUSL_TARGET=x86_64-unknown-linux-musl   # aarch64-…-musl on ARM
ARG CACHE_VERSION=""

###############################################################################
# build stage – fully static binary
###############################################################################
FROM dockerregistry.paycorp.co.za/base/rust:${RUST_VERSION}-alpine AS builder
ARG MUSL_TARGET

# Install build dependencies using HTTP to bypass corporate firewalls.
# 1. Overwrite the default repository list with HTTP URLs for both main and community.
# 2. Update the package list and install all required packages.
RUN echo "http://dl-cdn.alpinelinux.org/alpine/v$(cut -d. -f1-2 /etc/alpine-release)/main" > /etc/apk/repositories && \
    echo "http://dl-cdn.alpinelinux.org/alpine/v$(cut -d. -f1-2 /etc/alpine-release)/community" >> /etc/apk/repositories && \
    apk update && \
    apk add --no-cache \
      build-base \
      cmake \
      perl \
      pkgconfig \
      openssl-dev \
      openssl-libs-static \
      ca-certificates && \
    update-ca-certificates && \
    adduser -D -u 1000 appuser

RUN rustup target add ${MUSL_TARGET} || true
# These ENV vars help the openssl crate find and link the static libraries
ENV OPENSSL_STATIC=1
ENV PKG_CONFIG_ALLOW_CROSS=1

WORKDIR /app

# ----------------------------------------------------------------------------
# Copy dependency manifests first for better caching
# ----------------------------------------------------------------------------
COPY backend/Cargo.toml backend/Cargo.lock ./

# Pre-build dependencies (this layer will be cached if deps don't change)
RUN --mount=type=cache,target=/usr/local/cargo/registry \
    --mount=type=cache,target=/app/target \
    mkdir -p src && \
    echo "fn main() {}" > src/main.rs && \
    echo "// Dummy lib.rs for dependency pre-build" > src/lib.rs && \
    cargo build --release --locked --target ${MUSL_TARGET} && \
    rm -rf src

# ----------------------------------------------------------------------------
# Copy only source code (excluding tests, coverage, docs, etc.)
# ----------------------------------------------------------------------------
COPY backend/src/ ./src/

# Build the actual application
RUN --mount=type=cache,target=/usr/local/cargo/registry \
    --mount=type=cache,target=/app/target \
    touch src/main.rs && \
    cargo build --release --locked \
        --package myatm-embedded-ui \
        --bin     myatm-embedded-ui \
        --target  ${MUSL_TARGET} \
 && cp target/${MUSL_TARGET}/release/myatm-embedded-ui /tmp/myatm-embedded-ui \
 && strip /tmp/myatm-embedded-ui \
 && chmod +x /tmp/myatm-embedded-ui

###############################################################################
# frontend bundling stage
###############################################################################
FROM node:22-alpine AS frontend-builder
ARG CACHE_VERSION

# Install dependencies
RUN apk add --no-cache \
    bash \
    git

WORKDIR /frontend

# Copy package.json and package-lock.json first for better caching
COPY frontend/package*.json ./

# Install npm dependencies (including devDependencies for build)
RUN npm ci

# Copy frontend source files
COPY frontend/ ./

# Set environment variables for build
ENV CACHE_VERSION=${CACHE_VERSION}
ENV NODE_ENV=production

# Build and bundle the frontend
RUN set -x && \
    # Generate cache version if not provided
    if [ -z "$CACHE_VERSION" ]; then \
        export CACHE_VERSION=$(date +%Y%m%d%H%M%S); \
    fi && \
    echo "Building frontend with cache version: $CACHE_VERSION" && \
    # Run the build process (build.js handles deposits.html processing)
    npm run build && \
    # List the output files
    echo "Build output:" && \
    ls -la dist/ && \
    echo "Frontend bundling complete"

# Sanity-check (non-fatal while debugging)
RUN ls -R dist | grep 'deposits/deposits.html' || echo "⚠️ deposits.html missing"

###############################################################################
# runtime stage – scratch image
###############################################################################
FROM scratch
ARG MUSL_TARGET

COPY --from=builder /etc/ssl/certs/ca-certificates.crt /etc/ssl/certs/ca-certificates.crt
COPY --from=builder /tmp/myatm-embedded-ui /myatm-embedded-ui
COPY --from=frontend-builder /frontend/dist ./static

EXPOSE 8000
ENTRYPOINT ["/myatm-embedded-ui"]
