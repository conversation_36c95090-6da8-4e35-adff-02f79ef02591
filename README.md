# myATM Embedded UI

Embedded user interfaces for the myATM Merchant Portal that are hosted as iframes within the main portal application. This project represents a complete rewrite with a focus on **performance** and **simplicity**.

## 🎯 Project Overview

This application replaces the previous implementation that was based on .NET and Angular, which didn't properly implement the **Backend-for-Frontend (BFF)** pattern. The new architecture provides:

- **True BFF Implementation**: Proper abstraction layer between frontend and backend services
- **Rust Backend**: High-performance, memory-safe API layer
- **Vanilla JavaScript Frontend**: Minimal overhead with professional UI components
- **Containerized Deployment**: Lightweight Docker containers for Kubernetes deployment
- **iframe Integration**: Seamless embedding within the myATM Merchant Portal

## 🏗️ Architecture

The application follows a proper Backend-for-Frontend pattern:

- **Frontend**: Vanilla JS with Syncfusion components for professional UI
- **BFF Layer**: Rust-based API proxy that handles authentication, data transformation, and service orchestration
- **External Services**: Integration with various myATM backend services via the BFF

## 📁 Project Structure

```
myatm-embedded-ui/
├── backend/
│   ├── Cargo.toml              # Rust dependencies
│   └── src/
│       ├── main.rs             # Main Axum server
│       ├── elasticsearch_layer.rs # Logging integration
│       └── monitoring.rs       # Health checks and metrics
├── frontend/
│   ├── index.html              # Main HTML with Syncfusion imports
│   └── app.js                  # JavaScript for data fetching & grid setup
├── k8s/
│   ├── deployment-qa.yaml      # Kubernetes deployment config
│   ├── service-qa.yaml         # Kubernetes service config
│   └── otel-collector-config-qa.yaml # OpenTelemetry configuration
├── Dockerfile                  # Multi-stage build for production
└── README.md                   # This file
```

## 🚀 Quick Start

### Development

**Windows (PowerShell):**
```powershell
# Use the development script
.\dev.ps1

# Or build and run with Docker
.\build.ps1
.\run.ps1
```

### Production Deployment

```bash
# Build the Docker image
docker build -t myatm-embedded-ui .

# Deploy to Kubernetes (QA environment)
kubectl apply -f k8s/
```

## 🔧 How It Works

1. **Portal Integration**: The main myATM Merchant Portal loads this application in an iframe
2. **Authentication**: Parent portal handles authentication and passes tokens to the embedded UI
3. **BFF Processing**: Rust backend receives requests and orchestrates calls to various myATM services
4. **Data Presentation**: Frontend renders data using professional Syncfusion components
5. **Monitoring**: Full observability with OpenTelemetry, Elasticsearch logging, and health checks

## 🛠️ Technology Stack

- **Backend**: Rust + Axum + Tokio + Reqwest
- **Frontend**: Vanilla JavaScript + Syncfusion Grid
- **Container**: Docker with multi-stage build (`scratch` base for minimal footprint)
- **Deployment**: Kubernetes with OpenTelemetry collector sidecar
- **Monitoring**: Elasticsearch + APM integration
- **Configuration**: Environment-based configuration with .env support

## 🧪 Testing & Code Coverage

This project includes comprehensive unit tests with code coverage analysis:

- **Test Suite**: Unit tests located in `backend/tests/` covering configuration, handlers, and OData parsing
- **Coverage Tool**: Uses `cargo-tarpaulin` for accurate code coverage reporting
- **Test Runner**: Use `.\run-tests.ps1` to execute tests with coverage analysis
- **Reports**: Generates HTML and LCOV coverage reports in `backend/coverage/`

```powershell
# Run tests with coverage (basic usage)
.\run-tests.ps1

# Run with options
.\run-tests.ps1 -Verbose -Open -Clean
```

For detailed testing information, test structure, and coverage integration, see [README_TESTS.md](README_TESTS.md).

## 📝 Manual Testing

### Deposits

#### Standard Deposit Grid

To manually test the standard deposit grid, you can use the following URL in your browser:

```
http://localhost:8000/deposits?terminalId=ATMH0007
```

This page will load the standard deposit grid for the specified terminal ID, allowing you to verify UI functionality, data loading, and user interactions as they would appear in a typical deployment scenario.

#### Multi-Merchant Deposit Grid

To test the multi-merchant deposit grid scenario, use the following URL:

```
http://localhost:8000/deposits?terminalId=ATMH0582&multiMerchant=true
```

This page will load the deposit grid in multi-merchant mode for the specified terminal, allowing you to verify multi-merchant features and behaviors.

#### MCA (Merchant Cash-Advance) Mode

To test the MCA deposit grid functionality, use the following URL:

```
http://localhost:8000/deposits?terminalId=ATMH0794&mca=true
```

This page will load the deposit grid in MCA mode showing the specialized column set:
- Deposit Date | Terminal ID | Depositor Name | Trace No | Deposited | Capital Express Deduction | Net Settlement

**Note**: If both `multiMerchant=true` and `mca=true` are supplied, multi-merchant mode takes precedence.

## 📊 API Endpoints

- `GET /` - Serves the frontend application
- `GET /api/data` - Data endpoint proxied through BFF
- `GET /health/live` - Liveness probe for Kubernetes
- `GET /health/ready` - Readiness probe for Kubernetes

### Proxy Endpoints

The BFF includes a proxy endpoint that forwards requests to the external Merchant Deposits API:

- **GET /api/deposits** - Proxies OData v4 queries to the external deposits API
  - Supports query parameters: `terminalId`, `multiMerchant=true`, `mca=true`
  - Endpoint selection: `multiMerchant` takes precedence over `mca` when both are present
- **GET /api/proxy/health** - Health check for proxy functionality

#### Configuration

The proxy requires the following environment variables:

```env
# External API Configuration
EXTERNAL_API_URL=https://merchant-deposits-myatm-api.qaoffice.co.za
EXTERNAL_API_TIMEOUT=30

# Authentication
BEARER_TOKEN=your-bearer-token-here
```

#### Example Usage

```bash
# Get deposits with OData filter
curl -H "Authorization: Bearer your-token" \
  "http://localhost:8000/api/deposits?$count=true&$filter=(TerminalId%20eq%20%27CXBH0006%27)%20and%20(DepositDateTime%20ge%202025-02-01T00%3A00%3A00.00Z%20and%20DepositDateTime%20le%202025-02-28T23%3A59%3A59.00Z)&$orderby=SubmitSettlementDate%20desc,DepositDateTime%20desc"

# Check proxy health
curl http://localhost:8000/api/proxy/health
```

The proxy automatically:
- Forwards all query parameters to the external API
- Adds Bearer token authentication
- Handles timeouts and error responses
- Preserves response headers and content

## 🐳 Docker Details

Multi-stage build process:
1. **Builder Stage**: Compiles Rust binary with musl for static linking
2. **Runtime Stage**: Uses `scratch` base image with minimal footprint

Final image size: **~6MB** with no shell or runtime dependencies.

## 🔒 Security & Performance Benefits

- **API Security**: External service credentials hidden from frontend
- **Performance**: Rust backend provides excellent throughput and low latency
- **Resource Efficiency**: Minimal memory footprint compared to .NET implementation
- **Proper BFF**: Authentication, authorization, and data transformation handled server-side
- **Caching**: Strategic caching at the BFF layer reduces backend service load

## 🚫 Cache Busting & Asset Versioning

To ensure users always receive the latest JavaScript and CSS files (while still benefiting from browser caching when assets are unchanged), this project implements a robust cache busting and asset versioning system:

- **Automatic Versioning**: All static assets (JS/CSS) are served with a version query parameter (e.g., `app.js?v=20250703114107`). The version is updated on every build or deployment.
- **Cache Control Strategy**:
  - **Versioned JS/CSS**: `max-age=2592000, immutable` (30 days)
  - **Non-versioned JS/CSS**: `max-age=300` (5 minutes)
  - **HTML files**: `max-age=0, must-revalidate` (no caching)
  - **Other assets**: `max-age=86400` (1 day)
- **No More Stale Assets**: When a new version is deployed, browsers will always fetch the latest scripts and styles, eliminating "stale cache" bugs.

### Implementation Approaches

- **Basic**: PowerShell scripts and utilities for manual versioning and HTML updates
- **Docker**: Multi-stage Docker build automatically injects version parameters during image creation
- **Axum Backend**: Advanced server-side cache busting with API endpoints, real-time version checks, and client-side helpers

### Developer & User Benefits
- **Zero manual cache clearing**: Users always get the latest code after deployment
- **Safe long-term caching**: Browsers aggressively cache assets until a new version is released
- **Easy integration**: Works out-of-the-box for both local and containerized deployments
- **Advanced features**: Real-time update detection and cache refresh controls in development

For advanced usage, API details, and integration guides, see the documentation in [`docs/cache_busting/`](docs/cache_busting/).

## 🚀 Deployment

### QA Environment
```bash
# Deploy to QA namespace
kubectl apply -f k8s/deployment-qa.yaml
kubectl apply -f k8s/service-qa.yaml
```

### Environment Variables
Key configuration:
- `APM_SERVICE_NAME`: Service name for APM tracking
- `ELASTICSEARCH_URL`: Logging endpoint
- `LOG_LEVEL`: Logging verbosity

## 📈 Development Workflow

1. **Backend Changes**: Edit `backend/src/` files and test with `.\dev.ps1`
2. **Frontend Changes**: Edit `frontend/` files (automatically served during development)
3. **Kubernetes Config**: Update `k8s/` files for deployment changes
4. **Dependencies**: Update `backend/Cargo.toml` for new Rust crates

## 🔄 Migration from Previous Implementation

This rewrite addresses several issues from the previous .NET/Angular implementation:

- ✅ **Proper BFF Pattern**: Server-side data transformation and service orchestration
- ✅ **Performance**: Significant reduction in memory usage and response times
- ✅ **Simplicity**: Reduced complexity with vanilla JS instead of Angular framework
- ✅ **Container Size**: Minimal Docker images vs. large .NET runtime images
- ✅ **Resource Usage**: Lower CPU and memory requirements for Kubernetes deployment

## 🤝 Contributing

This is an internal myATM project. For changes:

1. Create feature branches following the pattern: `feat/description/username`
2. Test locally using the development scripts
3. Deploy to QA environment for testing before production release 